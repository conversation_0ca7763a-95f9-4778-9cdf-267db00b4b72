import React, { ReactNode } from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';

interface Props {
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  label?: string;
  onPress?: () => void;
  LeftIconContainer?: ViewStyle;
  rightIconContainer?: ViewStyle;
  labelStyle?: TextStyle;
  containerStyle?: ViewStyle;
  disabled?: boolean;
}

const ButtonwithIcon: React.FC<Props> = ({
  label,
  onPress,
  rightIcon,
  leftIcon,
  LeftIconContainer,
  rightIconContainer,
  labelStyle,
  containerStyle,
  disabled,
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      disabled={disabled}
      style={[styles.container, containerStyle]}
    >
      {leftIcon && <View style={LeftIconContainer}>{leftIcon}</View>}
      {label && <Text style={[styles.label, labelStyle]}>{label}</Text>}
      {rightIcon && <View style={rightIconContainer}>{rightIcon}</View>}
    </TouchableOpacity>
  );
};

export default ButtonwithIcon;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
  },
  label: {
    color: colors.light_Secondary,
    fontFamily: fonts.NotoSans_Regular,
    fontSize: 16,
  },
});
