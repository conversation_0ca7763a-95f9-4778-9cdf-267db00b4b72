<svg width="32" height="30" viewBox="0 0 32 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1013_103260)">
<rect x="6.33301" y="6" width="19" height="18" rx="5" fill="#9ADBF2" shape-rendering="crispEdges"/>
<path d="M13.0104 15.71V14.93H15.4304V15.71H13.0104ZM16.233 15.71V14.93H18.653V15.71H16.233Z" fill="white"/>
</g>
<defs>
<filter id="filter0_d_1013_103260" x="0.333008" y="0" width="31" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1013_103260"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1013_103260" result="shape"/>
</filter>
</defs>
</svg>
