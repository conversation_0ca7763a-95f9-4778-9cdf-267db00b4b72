import {
  Image,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useState } from 'react';
import TopAnimatedTab from '../../../componets/common/TopAnimatedTab';
import { appStyles, colors, sizes } from '../../../theme/theme';
import {
  AppText,
  BloodPressureGraph,
  HealthAverages,
  HealthLogs,
  HealthTipsList,
  InfoCard,
  TimelineFilter,
} from '../../../componets';
import Animated, {
  FadeInRight,
  FadeOutRight,
  LayoutAnimationConfig,
} from 'react-native-reanimated';
import AnimatedTabs from '../../../componets/common/AnimatedTabs';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { images } from '../../../assets/images';
type Props = NativeStackScreenProps<StackParamList, 'BloodPressure'>;

const BloodPressure: React.FC<Props> = ({ navigation }) => {
  // State variables
  const [activeTab, setActiveTab] = useState<string>('Logs');
  const [selectedRange, setSelectedRange] = useState('1Week');
  const [healthTabs, setHealthTabs] = useState('Blood Pressure');

  // Tab options
  const TABS = ['Logs', 'Graph'];
  const HEALTH_TABS = ['Blood Pressure', 'Glucose', 'Pulse'];

  // Sample data
  const BloodGlucoseData: any = [
    {
      id: '1',
      value: '128 / 88',
      tags: ['Auto measurement', 'Stress', 'Left arm'],
      recordedAt: '02/28/2024 at 12:30 am',
      status: 'Normal',
      title: 'Blood Pressure',
    },
  ];

  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <StatusBar
        backgroundColor={colors.background_color}
        barStyle={'dark-content'}
      />

      {/* Top Tabs */}
      <View style={styles.topTabsContainer}>
        <TopAnimatedTab
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          TABS={TABS}
        />
      </View>

      {/* Animated tab content */}
      <LayoutAnimationConfig>
        <Animated.View
          key={`tab-content-${activeTab}`}
          {...(Platform.OS === 'ios' && {
            entering: FadeInRight.springify().damping(80).stiffness(200),
            exiting: FadeOutRight.springify().damping(80).stiffness(200),
          })}
          style={styles.tabContent}
        >
          {activeTab === 'Logs' ? (
            // Logs View
            <HealthLogs
              data={BloodGlucoseData}
              isBloodPressure
              containerStyle={styles.logsContainer}
              onPressLogs={() => navigation.navigate('BPDetails')}
              onPressButton={() => navigation.navigate('FindBPDevices')}
            />
          ) : (
            // Graph View
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.contentContainer}
            >
              {/* Time Range Filter */}
              <TimelineFilter
                selectedRange={selectedRange}
                setSelectedRange={setSelectedRange}
                containerStyle={styles.timelineFilter}
              />
              {/* Blood Pressure Graph */}
              <BloodPressureGraph />

              {/* Health Category Tabs */}
              <AnimatedTabs
                activeTab={healthTabs}
                setActiveTab={setHealthTabs}
                TABS={HEALTH_TABS}
                containerStyle={styles.healthTabs}
                backgroundColor="#0873B41A"
                activeColor={colors.grey_90}
                unActiveColor={colors.grey_90}
              />

              {/* Average mmHg & A1C Card */}
              <HealthAverages isBloodPressure />
  {/* BLOOD GLUCOSE CHART */}
        <View style={styles.chartContainer}>
          <Image
            source={images.BPChartImage}
            resizeMode="contain"
            style={{ height: 250, width: '100%' }}
          />
        </View>
              {/* Analysis Info */}
              <InfoCard
                title="Analysis Blood Glucose"
                description="It’s important to note that individual variations can occur, and what’s considered normal can differ slightly based on factors such as age, gender and overall health. With your provided data, we are pleased to inform you that you are having a good health."
                actionText="Continue monitoring your blood Glucose level periodically, ensuring you maintain a balanced diet and regular physical activity to keep these readings stable."
              />

              {/* Disclaimer Info */}
              <InfoCard
                title="Disclaimer"
                description="The device is for informational purposes only and should not be used for medical purpose"
              />

              {/* Section Header: Helpful for You */}
              <View style={styles.rowContainer}>
                <AppText style={styles.measurementTitle}>
                  Helpful for you
                </AppText>
                <TouchableOpacity activeOpacity={0.5}>
                  <AppText style={styles.seeAllText}>See all</AppText>
                </TouchableOpacity>
              </View>

              {/* Health Tips List */}
              <HealthTipsList />
            </ScrollView>
          )}
        </Animated.View>
      </LayoutAnimationConfig>
    </View>
  );
};

export default BloodPressure;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  topTabsContainer: {
    paddingHorizontal: sizes.paddingHorizontal,
    marginTop: 12,
  },
  tabContent: {
    flex: 1,
    paddingTop: 12,
  },
  logsContainer: {
    marginTop: 22,
  },
  contentContainer: {
    paddingBottom: 60,
  },
  timelineFilter: {
    alignSelf: 'center',
    marginTop: 22,
    marginBottom: 0,
  },
  healthTabs: {
    marginTop: 34,
    marginHorizontal: sizes.paddingHorizontal,
  },
  rowContainer: {
    ...appStyles.flexBtw,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 34,
  },
  measurementTitle: {
    ...appStyles.h3,
    color: colors.grey_100,
  },
  seeAllText: {
    ...appStyles.body2,
    fontSize: 12,
    color: colors.primary,
  },
  chartContainer: {
    backgroundColor: colors.white,
    marginHorizontal: sizes.paddingHorizontal,
    marginTop : 34,
    paddingHorizontal: 8,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  }
});
