import React from 'react';
import {
  View,
  Text,
  FlatList,
  Image,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { colors, sizes } from '../../theme/theme';

const tips = [
  {
    id: '1',
    title: 'What is Mild Diabetes?',
    image:
      'https://plus.unsplash.com/premium_photo-1713962952353-d72f194a8650?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8V2hhdCUyMGlzJTIwTWlsZCUyMERpYWJldGVzJTNGfGVufDB8fDB8fHww',
  },
  {
    id: '2',
    title: 'Pre-diabetes & Gestational Diabetes',
    image:
      'https://images.unsplash.com/photo-1527206849040-ae3110e4ff88?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTV8fFByZSUyMGRpYWJldGVzJTIwJTI2JTIwR2VzdGF0aW9uYWwlMjBEaWFiZXRlc3xlbnwwfHwwfHx8MA%3D%3D',
  },
  {
    id: '3',
    title: 'Best Foods to Control Sugar',
    image:
      'https://images.unsplash.com/photo-1582232655383-0826ba4f1347?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8QmVzdCUyMEZvb2RzJTIwdG8lMjBDb250cm9sJTIwU3VnYXJ8ZW58MHx8MHx8fDA%3D',
  },
  {
    id: '4',
    title: 'Symptoms of Type 2 Diabetes',
    image:
      'https://plus.unsplash.com/premium_photo-1674586934107-0205a87a4406?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8U3ltcHRvbXMlMjBvZiUyMFR5cGUlMjAyJTIwRGlhYmV0ZXN8ZW58MHx8MHx8fDA%3D',
  },
  {
    id: '5',
    title: 'How to Prevent Diabetes',
    image:
      'https://plus.unsplash.com/premium_photo-1698846880685-4b8b54c28ad3?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8SG93JTIwdG8lMjBQcmV2ZW50JTIwRGlhYmV0ZXN8ZW58MHx8MHx8fDA%3D',
  },
];

const ITEM_WIDTH = 187;
const ITEM_HEIGHT = 124;

const HealthTipsList = () => {
  return (
    <FlatList
      data={tips}
      horizontal
      keyExtractor={item => item.id}
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
      renderItem={({ item }) => (
        <View style={styles.item}>
          <Image
            source={{ uri: item.image }}
            style={styles.image}
            resizeMode="cover"
          />
          <Text style={styles.title}>{item.title}</Text>
        </View>
      )}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: sizes.paddingHorizontal,
    gap: 12,
    marginTop: 12,
  },
  item: {
    width: ITEM_WIDTH,
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    marginBottom: 2,
    paddingBottom: 12,
  },
  image: {
    width: ITEM_WIDTH,
    height: ITEM_HEIGHT,
    marginBottom: 8,
    borderTopRightRadius: 12,
    borderTopLeftRadius: 12,
  },
  title: {
    fontSize: 14,
    color: colors.grey_30,
    paddingHorizontal: 10,
  },
});

export default HealthTipsList;
