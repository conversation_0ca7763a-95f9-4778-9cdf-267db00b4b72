import {
  Image,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useState } from 'react';
import TopAnimatedTab from '../../../componets/common/TopAnimatedTab';
import { appStyles, colors, sizes } from '../../../theme/theme';
import Animated, {
  FadeInRight,
  FadeOut,
  FadeOutRight,
  LayoutAnimationConfig,
} from 'react-native-reanimated';
import {
  AppButton,
  AppText,
  HealthLogs,
  HealthTipsList,
  InfoCard,
  WeightIndicator,
} from '../../../componets';
import { images } from '../../../assets/images';
import MetricsGrid from '../../../componets/home/<USER>';
import DonutChartSection from '../../../componets/home/<USER>';
import WeightManagementChart from '../../../componets/home/<USER>';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'WeightManagement'>;

const WeightManagement: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<string>('Status');
  const [expanded, setExpanded] = useState(false); // Toggle detailed view
  const WeightData: any = [
    {
      id: '1',
      value: 100,
      tags: ['Manual measurement'],
      recordedAt: ' 02/12/2024 at 10:30 am',
      status: 'Obese',
      title: 'Weight',
    },
  ];
  // Tab options
  const TABS = ['Status', 'Logs', 'Graph'];
  return (
    <View style={styles.container}>
      {/* Status Bar */}
      <StatusBar
        backgroundColor={colors.background_color}
        barStyle={'dark-content'}
      />
      <AppButton
        title="SetWeightGoal"
        onPress={() => navigation.navigate('SetWeightGoal')}
      />
      <AppButton
        title="MeasureWeightManual"
        onPress={() => navigation.navigate('MeasureWeightManual')}
      />
      {/* Top Tabs */}
      <View style={styles.topTabsContainer}>
        <TopAnimatedTab
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          TABS={TABS}
        />
      </View>
      {/* Animated tab content */}
      <LayoutAnimationConfig>
        <Animated.View
          key={`tab-content-${activeTab}`}
          {...(Platform.OS === 'ios' && {
            entering: FadeInRight.springify().damping(80).stiffness(200),
            exiting: FadeOutRight.springify().damping(80).stiffness(200),
          })}
          style={styles.tabContent}
        >
          {activeTab === 'Logs' ? (
            <HealthLogs
              data={WeightData}
              isWeight={true}
              containerStyle={styles.logsContainer}
              onPressLogs={() => navigation.navigate('WeightManagementDetails')}
              onPressButton={() => navigation.navigate('WeightDevices')}
            />
          ) : (
            <ScrollView
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.contentContainer}
            >
              {activeTab === 'Status' && <DonutChartSection />}
              {activeTab === 'Graph' && <WeightManagementChart />}
              <WeightIndicator
                title={'Weight Loss'}
                label1="Total Loss"
                value1="33.2"
                unit1="kg"
                label2="Progress"
                value2="0"
                unit2="%"
                label3="Goal"
                value3="85.0"
                unit3="kg"
              />
              <WeightIndicator
                title={'Current Weight'}
                label1="Total Loss"
                value1="33.2"
                unit1="kg"
                label2="Progress"
                value2="0"
                unit2="%"
                label3="Goal"
                value3="85.0"
                unit3="kg"
                isIndicator
                expanded={expanded}
                setExpanded={setExpanded}
              />
              {expanded ? (
                <Animated.View
                  entering={FadeInRight.springify().damping(80).stiffness(200)}
                  exiting={FadeOut.springify().damping(80).stiffness(200)}
                >
                  <MetricsGrid />
                </Animated.View>
              ) : null}
              {/* ↑↓ */}
              <WeightIndicator
                title={'Avg'}
                label1="Weight"
                value1="33.2"
                unit1="↑"
                label2="Progress"
                value2="0"
                unit2="↑"
                label3="Goal"
                value3="85.0"
                unit3="↓"
              />

              {/* Body Mass CHART */}
              <View style={styles.chartContainer}>
                <Image
                  source={images.BodyMassIndex}
                  resizeMode="contain"
                  style={{ height: 250, width: '100%' }}
                />
              </View>
              {/* Analysis Info */}
              <InfoCard
                title="Analysis Blood Glucose"
                description="It’s important to note that individual variations can occur, and what’s considered normal can differ slightly based on factors such as age, gender and overall health. With your provided data, we are pleased to inform you that you are having a good health."
                actionText="Continue monitoring your blood Glucose level periodically, ensuring you maintain a balanced diet and regular physical activity to keep these readings stable."
              />

              {/* Disclaimer Info */}
              <InfoCard
                title="Disclaimer"
                description="The device is for informational purposes only and should not be used for medical purpose"
              />

              {/* Section Header: Helpful for You */}
              <View style={styles.rowContainer}>
                <AppText style={styles.measurementTitle}>
                  Helpful for you
                </AppText>
                <TouchableOpacity activeOpacity={0.5}>
                  <AppText style={styles.seeAllText}>See all</AppText>
                </TouchableOpacity>
              </View>

              {/* Health Tips List */}
              <View>
                <HealthTipsList />
              </View>
            </ScrollView>
          )}
        </Animated.View>
      </LayoutAnimationConfig>
    </View>
  );
};

export default WeightManagement;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  topTabsContainer: {
    paddingHorizontal: sizes.paddingHorizontal,
    marginTop: 12,
  },
  tabContent: {
    flex: 1,
    paddingTop: 12,
  },
  rowContainer: {
    ...appStyles.flexBtw,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 34,
  },
  measurementTitle: {
    ...appStyles.h3,
    color: colors.grey_100,
  },
  seeAllText: {
    ...appStyles.body2,
    fontSize: 12,
    color: colors.primary,
  },
  chartContainer: {
    backgroundColor: colors.white,
    marginHorizontal: sizes.paddingHorizontal,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    borderRadius: 12,
    marginTop: 34,
    paddingHorizontal: 10,
  },
  contentContainer: {
    paddingBottom: 60,
  },
  logsContainer: {
    marginTop: 24,
  },
});
