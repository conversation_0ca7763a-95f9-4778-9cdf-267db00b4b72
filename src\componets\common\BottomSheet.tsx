import React, {
  forwardRef,
  useImperativeHandle,
  useMemo,
  useCallback,
  ReactNode,
} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from 'react-native';
import {
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetView,
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
} from '@gorhom/bottom-sheet';
import { appStyles, colors, fonts } from '../../theme/theme';
import AppText from './AppText';

export interface BottomSheetRef {
  present: () => void;
  dismiss: () => void;
}

interface BottomSheetProps {
  children: ReactNode;
  title?: string;
  snapPoints?: string[];
  enablePanDownToClose?: boolean;
  onDismiss?: () => void;
  onClose?: () => void;
  containerStyle?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showCloseButton?: boolean;
  backdropOpacity?: number;
  titleStyle?: TextStyle;
}

const CustomBottomSheet = forwardRef<BottomSheetRef, BottomSheetProps>(
  (
    {
      children,
      title,
      snapPoints = ['50%'],
      enablePanDownToClose = true,
      onDismiss,
      onClose,
      containerStyle,
      contentContainerStyle,
      showCloseButton = false,
      backdropOpacity = 0.5,
      titleStyle,
    },
    ref,
  ) => {
    const bottomSheetModalRef = React.useRef<BottomSheetModal>(null);

    // Memoize snap points
    const snapPointsMemo = useMemo(() => snapPoints, [snapPoints]);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      present: () => {
        bottomSheetModalRef.current?.present();
      },
      dismiss: () => {
        bottomSheetModalRef.current?.dismiss();
      },
    }));

    // Handle dismiss
    const handleDismiss = useCallback(() => {
      onDismiss?.();
    }, [onDismiss]);

    // Handle close
    const handleClose = useCallback(() => {
      onClose?.();
    }, [onClose]);

    // Custom backdrop component
    const renderBackdrop = useCallback(
      (props: BottomSheetBackdropProps) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          opacity={backdropOpacity}
        />
      ),
      [backdropOpacity],
    );

    return (
      <BottomSheetModalProvider>
        <BottomSheetModal
          ref={bottomSheetModalRef}
          index={0}
          snapPoints={snapPointsMemo}
          enablePanDownToClose={enablePanDownToClose}
          onDismiss={handleDismiss}
          backdropComponent={renderBackdrop}
          backgroundStyle={styles.backgroundStyle}
          handleIndicatorStyle={styles.handleIndicator}
          style={[styles.container, containerStyle]}
        >
          <BottomSheetView
            style={[styles.contentContainer, contentContainerStyle]}
          >
            {/* Header */}
            {(title || showCloseButton) && (
              <View style={styles.header}>
                <View style={styles.titleContainer}>
                  {title && (
                    <AppText style={[styles.title, titleStyle]}>
                      {title}
                    </AppText>
                  )}
                </View>
                {showCloseButton && (
                  <TouchableOpacity
                    onPress={handleClose}
                    style={styles.closeButton}
                    activeOpacity={0.7}
                  >
                    <AppText style={styles.closeButtonText}>✕</AppText>
                  </TouchableOpacity>
                )}
              </View>
            )}

            {/* Content */}
            <View style={styles.content}>{children}</View>
          </BottomSheetView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    );
  },
);

// Provider wrapper component
export const BottomSheetProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  return <BottomSheetModalProvider>{children}</BottomSheetModalProvider>;
};

export default CustomBottomSheet;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backgroundStyle: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
  handleIndicator: {
    backgroundColor: colors.grey_05,
    width: 60,
    height: 4,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.grey_10,
    marginBottom: 16,
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    ...appStyles.h2,
    textAlign: 'center',
    color: colors.grey_95,
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    // backgroundColor: colors.grey_10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    color: colors.grey_60,
    fontFamily: fonts.Catamaran_SemiBold,
  },
  content: {
    flex: 1,
  },
});

CustomBottomSheet.displayName = 'BottomSheet';
