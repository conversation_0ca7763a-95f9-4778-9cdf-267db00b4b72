import {
  Dimensions,
  StyleSheet,
  View,
  TouchableOpacity,
  Animated,
  ViewStyle,
} from 'react-native';
import React, { useEffect, useRef } from 'react';
import { colors, fonts } from '../../theme/theme';

interface Props {
  activeTab: string;
  setActiveTab: (val: string) => void;
  TABS: string[];
  containerStyle?: ViewStyle;
}

const TopAnimatedTab: React.FC<Props> = ({
  activeTab,
  setActiveTab,
  TABS,
  containerStyle,
}) => {
  const screenWidth = Dimensions.get('screen').width;
  const dividedNum = TABS.length == 2 ? 24 : 16;

  const tabWidth = screenWidth / TABS?.length - dividedNum;

  const translateX = useRef(new Animated.Value(0)).current;

  // Initialize animated values for each tab's font size and color
  const fontSizes = useRef(
    TABS.map(tab => new Animated.Value(tab === activeTab ? 1 : 0)),
  ).current;

  useEffect(() => {
    const activeIndex = TABS.findIndex(tab => tab === activeTab);

    // Animate indicator and font sizes
    Animated.parallel([
      Animated.timing(translateX, {
        toValue: activeIndex * tabWidth,
        duration: 400,
        useNativeDriver: true,
      }),
      ...fontSizes.map((animVal, i) =>
        Animated.timing(animVal, {
          toValue: i === activeIndex ? 1 : 0,
          duration: 300,
          useNativeDriver: false,
        }),
      ),
    ]).start();
  }, [activeTab, tabWidth]);

  return (
    <View style={[styles.tabsContainer, containerStyle]}>
      {/* Animated indicator */}
      <Animated.View
        style={[
          styles.indicator,
          {
            width: tabWidth,
            transform: [{ translateX }],
          },
        ]}
      />

      {TABS.map((tab, index) => {
        const fontSize = fontSizes[index].interpolate({
          inputRange: [0, 1],
          outputRange: [16, 14],
        });

        const color = fontSizes[index].interpolate({
          inputRange: [0, 1],
          outputRange: ['#808B9A', '#202326'],
        });

        return (
          <TouchableOpacity
            key={tab}
            style={[styles.tabContainer, { width: tabWidth }]}
            onPress={() => setActiveTab(tab)}
            disabled={activeTab == tab}
          >
            <Animated.Text
              style={{
                fontSize,
                color,
                fontFamily: fonts.NotoSans_Medium,
              }}
            >
              {tab}
            </Animated.Text>
          </TouchableOpacity>
        );
      })}
      <View
        style={{
          height: 2,
          backgroundColor: colors.grey_20,
          position: 'absolute',
          width: '100%',
          bottom: 1,
          borderRadius: 3,
        }}
      />
    </View>
  );
};

export default TopAnimatedTab;

const styles = StyleSheet.create({
  tabsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    position: 'relative',
    backgroundColor: colors.background_color,
    height: 48,
    overflow: 'hidden',
    // borderBottomWidth: 2,
    borderColor: '#D9DFE6',
    zIndex: 20,
  },
  tabContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  indicator: {
    position: 'absolute',
    height: 4,
    backgroundColor: colors.grey_90,
    bottom: 0,
    borderRadius: 10,
    left: 0,
    zIndex: 1000,
  },
});
