import React from 'react';
import { View, StyleSheet } from 'react-native';
import WheelPicker from '@quidone/react-native-wheel-picker';
import { colors, fonts, sizes } from '../../theme/theme';
import AppText from '../common/AppText';

type PickerItem = {
  value: number;
  label: string;
};

interface DurationPickerProps {
  numberData: PickerItem[];
  unitData: PickerItem[];
  selectedNumber: number;
  selectedUnit: number;
  setSelectedNumber: (val: number) => void;
  setSelectedUnit: (val: number) => void;
}

const DurationPicker: React.FC<DurationPickerProps> = ({
  numberData,
  unitData,
  selectedNumber,
  selectedUnit,
  setSelectedNumber,
  setSelectedUnit,
}) => {
  return (
    <View>
      <View style={styles.container}>
        <WheelPicker
          data={numberData}
          value={selectedNumber}
          onValueChanged={({ item: { value } }) => setSelectedNumber(value)}
          enableScrollByTapOnItem
          width={sizes.width / 2 - sizes.paddingHorizontal}
          itemHeight={34}
          overlayItemStyle={styles.leftOverlay}
          itemTextStyle={styles.itemText}
        />
        <WheelPicker
          data={unitData}
          value={selectedUnit}
          onValueChanged={({ item: { value } }) => setSelectedUnit(value)}
          enableScrollByTapOnItem
          width={sizes.width / 2 - sizes.paddingHorizontal}
          itemHeight={34}
          overlayItemStyle={styles.rightOverlay}
          itemTextStyle={styles.itemText}
        />
      </View>
      <AppText style={styles.recommendedContainer}>Recommended</AppText>
      <AppText style={styles.avgText}>
        Average weekly [body weight loss]
      </AppText>
      <AppText style={styles.title}>
        0.50<AppText style={styles.unitText}>{'   '}KG</AppText>
      </AppText>
    </View>
  );
};

export default DurationPicker;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: sizes.paddingHorizontal,
    marginTop: -12,
  },
  leftOverlay: {
    borderRadius: 0,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
  },
  rightOverlay: {
    borderRadius: 0,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
  },
  itemText: {
    fontSize: 20,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_90,
  },
  recommendedContainer: {
    backgroundColor: '#9ADBF24D',
    paddingHorizontal: 14,
    borderRadius: 12,
    alignSelf: 'center',
    fontSize: 12,
    color: colors.primary,
    paddingVertical: 4,
  },
  avgText: {
    fontSize: 16,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Medium,
    textAlign: 'center',
    paddingTop: 12,
  },
  title: {
    fontSize: 22,
    color: colors.primary,
    fontFamily: fonts.Catamaran_Bold,
    textAlign: 'center',
  },
  unitText: {
    fontSize: 10,
    fontFamily: fonts.NotoSans_Medium,
  },
});
