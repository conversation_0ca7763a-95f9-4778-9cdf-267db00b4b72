import React, { useState } from 'react';
import { View, Dimensions, Text, StyleSheet, Platform } from 'react-native';
import { LineChart } from 'react-native-gifted-charts';
import { colors, fonts } from '../../theme/theme';
import TimelineFilter from './TimelineFilter';

const screenWidth = Dimensions.get('window').width;
const customDataPoint = () => {
  return (
    <View
      style={{
        width: 20,
        height: 20,
        backgroundColor: '#9ADBF2',
        borderWidth: 4,
        borderRadius: 10,
        borderColor: colors.white,
      }}
    />
  );
};
const data = [
  { value: 130, label: 'Mon', hideDataPoint: true },
  { value: 130, label: 'Tue', hideDataPoint: true },
  { value: 130, label: 'Wed', hideDataPoint: true },
  { value: 130, label: 'Thu', hideDataPoint: true },
  {
    value: 130,
    label: 'Fri',
    customDataPoint: customDataPoint,
  },
  { value: 130, label: 'Sat', hideDataPoint: true },
  { value: 130, label: 'Sun', hideDataPoint: true },
];

const WeightManagementChart = () => {
  const [selectedRange, setSelectedRange] = useState('1Week');

  return (
    <View style={{ marginTop: 24 }}>
      {/* Time Range Filter */}
      <TimelineFilter
        selectedRange={selectedRange}
        setSelectedRange={setSelectedRange}
        containerStyle={styles.timelineFilter}
      />
      <View style={{ height: 350, marginTop: 2 }}>
        <LineChart
          data={data}
          curved
          areaChart
          thickness={6}
          height={300}
          color={'#bae8f7'}
          maxValue={160}
          noOfSections={2}
          startFillColor={'#CCF2FF4D'}
          endFillColor={'#9ADBF200'}
          startOpacity={0.6}
          endOpacity={0.1}
          spacing={60} // 🔼 Increased gap between points (30 -> 60)
          backgroundColor={colors.background_color}
          rulesColor="#45445980"
          rulesType="solid"
          initialSpacing={34} // 🔼 Better padding at start
          yAxisColor={colors.background_color}
          xAxisColor={colors.background_color}
          dataPointsHeight={20}
          dataPointsWidth={20}
          rulesLength={screenWidth * 2} // 🔼 Extends scrollable width
          // scrollAnimation
          disableScroll
          yAxisTextStyle={{
            fontSize: 12,
            color: '#454459',
            fontFamily: fonts.NotoSans_Medium,
          }}
          xAxisLabelTextStyle={{
            color: '#454459',
            fontSize: 12,
            marginLeft: 4,
            fontFamily: fonts.NotoSans_Medium,
          }}
        />
      </View>
    </View>
  );
};

export default WeightManagementChart;

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  readingText: {
    fontSize: 70,
    fontFamily: fonts.Catamaran_Bold,
    paddingHorizontal: 10,
    lineHeight: Platform.OS === 'ios' ? 100 : 80,
  },

  unitText: {
    fontSize: 20,
    color: colors.primary,
    fontFamily: fonts.Catamaran_Medium,
    lineHeight: 24,
  },
  timelineFilter: {
    alignSelf: 'center',
    marginBottom: 0,
  },
});
