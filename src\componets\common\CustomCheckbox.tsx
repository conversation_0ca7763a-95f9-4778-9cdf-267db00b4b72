import React from 'react';
import {
  Text,
  Pressable,
  StyleSheet,
  ViewStyle,
  TextStyle,
  View,
} from 'react-native';
import { CheckSqure, CheckStroke } from '../../assets/svgs';
import { fonts, colors } from '../../theme/theme';

type Props = {
  checked: boolean;
  onToggle: () => void;
  label: string | boolean;
  containerStyle?: ViewStyle;
  fillCheck?: {
    width: number;
    height: number;
  };
  strokeCheck?: {
    width: number;
    height: number;
  };
  labelStyles?: TextStyle;
  isRadioButton?: boolean;
  radioButtonStyle?: ViewStyle;
  radioButtonSelectedStyle?: ViewStyle;
  radioButtonInnerStyle?: ViewStyle;
};

const CustomCheckbox: React.FC<Props> = ({
  checked,
  onToggle,
  label,
  containerStyle,
  fillCheck,
  strokeCheck,
  labelStyles,
  isRadioButton = false,
  radioButtonStyle,
  radioButtonSelectedStyle,
  radioButtonInnerStyle,
}) => {
  const renderCheckbox = () => (
    <>
      {checked ? (
        <CheckSqure
          width={fillCheck?.width ? fillCheck?.width : 12}
          height={fillCheck?.height ? fillCheck?.height : 12}
        />
      ) : (
        <CheckStroke
          width={strokeCheck?.width ? strokeCheck?.width : 12}
          height={strokeCheck?.height ? strokeCheck?.height : 12}
        />
      )}
    </>
  );

  const renderRadioButton = () => (
    <View
      style={[
        styles.radioButton,
        radioButtonStyle,
        checked && styles.radioButtonSelected,
        checked && radioButtonSelectedStyle,
      ]}
    >
      {checked && (
        <View style={[styles.radioButtonInner, radioButtonInnerStyle]} />
      )}
    </View>
  );

  return (
    <Pressable onPress={onToggle} style={[styles.container, containerStyle]}>
      {isRadioButton ? renderRadioButton() : renderCheckbox()}
      <Text style={[styles.label, labelStyles]}>{label}</Text>
    </Pressable>
  );
};

export default CustomCheckbox;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  label: {
    fontSize: 14,
    color: '#858583',
    fontFamily: fonts.NotoSans_Regular,
    paddingTop: 2,
  },
  radioButton: {
    width: 17,
    height: 17,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.grey_50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonSelected: {
    borderColor: colors.primary,
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary,
  },
});
