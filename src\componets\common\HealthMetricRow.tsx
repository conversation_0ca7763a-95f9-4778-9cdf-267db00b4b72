import React from 'react';
import { View, StyleSheet, ViewStyle, TextStyle, Platform } from 'react-native';
import AppText from './AppText'; // Adjust path based on your file structure
import { colors, fonts } from '../../theme/theme';

type ValueRowProps = {
  label: string;
  value: string | number;
  unit?: string;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  valueStyle?: TextStyle;
  unitStyle?: TextStyle;
};

const HealthMetricRow = ({
  label,
  value,
  unit,
  containerStyle,
  labelStyle,
  valueStyle,
  unitStyle,
}: ValueRowProps) => {
  return (
    <View style={[styles.valueRow, containerStyle]}>
      <AppText style={[styles.valueLabel, labelStyle]}>{label}</AppText>
      <View style={styles.valueContainer}>
        <AppText style={[styles.valueInput, valueStyle]}>{value}</AppText>
        {unit ? (
          <AppText style={[styles.unit, unitStyle]}>{unit}</AppText>
        ) : null}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  valueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },

  valueLabel: {
    fontSize: 18,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_90,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  valueInput: {
    fontSize: 30,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_90,
    paddingRight: 12,
    marginTop: 2,
    lineHeight: 38,
    paddingTop: Platform.OS === 'android' ? 0 : 4,
  },
  unit: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.primary,
    marginLeft: 2,
  },
});

export default HealthMetricRow;
