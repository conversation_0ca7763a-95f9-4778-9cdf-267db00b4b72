import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  TextInput,
} from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import { AppButton, AppText } from '../../../componets';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

type Props = NativeStackScreenProps<StackParamList, 'ScanResult'>;

interface ScanData {
  glycemie: string;
  ketone: string;
}

const ScanResult: React.FC<Props> = ({ navigation, route }) => {
  const insets = useSafeAreaInsets();

  // Mock data - in real app this would come from route params or scanning result
  const [scanData, setScanData] = useState<ScanData>({
    glycemie: '138',
    ketone: '19',
  });

  const handleSave = () => {
    // TODO NAVIGATE TO BLOOD GLUCOSE DETAILS SCREEN
    navigation.pop(3);
  };

  const handleReScan = () => {
    // Handle re-scan logic here
    console.log('Re-scanning...');
    navigation.goBack();
  };

  const updateValue = (field: keyof ScanData, value: string) => {
    setScanData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      {/* Header Section */}
      <View style={styles.headerSection}>
        <AppText style={styles.title}>Vérifier les valeurs</AppText>

        <AppText style={styles.description}>
          Une fois les valeurs reconnues avec succès, elles s'affichent.
        </AppText>

        <AppText style={styles.description}>
          Si les valeurs ne sont pas correctes, vous pouvez les modifier avant
          d'enregistrer.
        </AppText>
      </View>

      {/* Values Section */}
      <View style={styles.valuesSection}>
        {/* Glycemie */}
        <View style={styles.valueRow}>
          <AppText style={styles.valueLabel}>Glycemie</AppText>
          <View style={styles.valueContainer}>
            <AppText style={styles.valueInput}>{scanData.glycemie}</AppText>
            <AppText style={styles.unit}>mg/dl</AppText>
          </View>
        </View>

        {/* Ketone */}
        <View style={styles.valueRow}>
          <AppText style={styles.valueLabel}>Ketone</AppText>
          <View style={styles.valueContainer}>
            <AppText style={styles.valueInput}>{scanData.ketone}</AppText>
            <AppText style={styles.unit}>mg/dl</AppText>
          </View>
        </View>
      </View>

      {/* Bottom Section */}
      <View style={styles.bottomSection}>
        {/* Save Button */}
        <AppButton
          title="Save"
          onPress={handleSave}
          containerStyle={styles.saveButton}
        />

        {/* Re-Scan Button */}
        <TouchableOpacity
          style={appStyles.strokeButtonContainer}
          onPress={handleReScan}
          activeOpacity={0.7}
        >
          <AppText style={appStyles.strokeButtonText}>Re-Scan</AppText>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ScanResult;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  headerSection: {
    paddingTop: 40,
    alignItems: 'center',
    marginBottom: 10,
  },
  title: {
    fontSize: 20,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.primary,
    textAlign: 'center',
    marginBottom: 24,
  },
  description: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_60,
    textAlign: 'center',
    marginBottom: 10,
    lineHeight: 24,
  },

  valuesSection: {
    flex: 1,
  },
  valueRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  valueLabel: {
    fontSize: 18,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_90,
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  valueInput: {
    fontSize: 30,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_90,
    textAlign: 'right',
    minWidth: 80,
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 4,
    // backgroundColor: colors.grey_10,
  },
  unit: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.primary,
    marginLeft: 2,
  },
  bottomSection: {
    paddingBottom: 30,
  },
  saveButton: {
    marginBottom: 16,
  },
});
