import { StyleSheet, View } from 'react-native';
import React from 'react';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { colors, sizes } from '../../theme/theme';
import { AppButton, FormInput, ModalPicker } from '../../componets';
import { StackParamList } from '../../navigations/StackNavigator';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
type Props = NativeStackScreenProps<StackParamList, 'UserProfile'>;

// Validation schema for the profile form
const UserProfileSchema = Yup.object().shape({
  firstName: Yup.string().required('First name is required.'),
  lastName: Yup.string().required('Last name is required.'),
  email: Yup.string().email('Invalid email').required('Email is required.'),
  password: Yup.string()
    .min(6, 'Min 6 characters')
    .required('Password is required.'),
  gender: Yup.string().required('Gender is required.'),
  dob: Yup.string().required('Date of birth is required.'),
  phone: Yup.string().required('Phone number is required.'),
});

const UserProfile: React.FC<Props> = ({ navigation }) => {
  const insets = useSafeAreaInsets();

  // Gender options for the modal picker
  const genderOptions = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  // useFormik hook to handle form logic
  const formik = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      password: '',
      gender: '',
      dob: '',
      phone: '',
    },
    validationSchema: UserProfileSchema,
    enableReinitialize: true,
    onSubmit: async values => {
      navigation.navigate('AccountCreationFeedback');
      console.log('Form Submitted:', values);
    },
  });

  // Destructure formik handlers
  const { handleChange, handleBlur, handleSubmit, values, errors, touched } =
    formik;

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={styles.scrollContent}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      <View style={[styles.container, { paddingBottom: insets.bottom + 64 }]}>
        {/* Form Inputs */}
        <View style={{ gap: 8 }}>
          <FormInput
            label="First name"
            value={values.firstName}
            onChangeText={handleChange('firstName')}
            onBlur={handleBlur('firstName')}
            error={touched.firstName ? errors.firstName : ''}
          />
          <FormInput
            label="Last name"
            value={values.lastName}
            onChangeText={handleChange('lastName')}
            onBlur={handleBlur('lastName')}
            error={touched.lastName ? errors.lastName : ''}
          />
          <FormInput
            label="Email"
            value={values.email}
            onChangeText={handleChange('email')}
            onBlur={handleBlur('email')}
            keyboardType="email-address"
            error={touched.email ? errors.email : ''}
          />
          <FormInput
            label="Password"
            value={values.password}
            onChangeText={handleChange('password')}
            onBlur={handleBlur('password')}
            secureTextEntry
            error={touched.password ? errors.password : ''}
          />
          <ModalPicker
            label="Gender"
            options={genderOptions}
            selectedValue={values.gender}
            onSelect={value => formik.setFieldValue('gender', value)}
            error={touched.gender ? errors.gender : ''}
          />
          <FormInput
            label="DOB"
            value={values.dob}
            onChangeText={handleChange('dob')}
            onBlur={handleBlur('dob')}
            error={touched.dob ? errors.dob : ''}
          />
          <FormInput
            label="Phone number"
            value={values.phone}
            onChangeText={handleChange('phone')}
            onBlur={handleBlur('phone')}
            keyboardType="phone-pad"
            error={touched.phone ? errors.phone : ''}
          />
        </View>

        {/* Submit Button */}
        <AppButton
          title="Create"
          onPress={handleSubmit}
          containerStyle={styles.loginButton}
          disabled={!formik.isValid || !formik.dirty}
        />
      </View>
    </KeyboardAwareScrollView>
  );
};

export default UserProfile;

// Styles
const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: colors.background_color,
    paddingTop: 12,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  loginButton: {
    marginTop: 20,
  },
});
