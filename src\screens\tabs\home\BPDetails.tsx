import {
  Image,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React from 'react';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import { StackParamList } from '../../../navigations/StackNavigator';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import {
  AppText,
  DiabetesIndicator,
  HealthTipsList,
  InfoCard,
} from '../../../componets';
import { images } from '../../../assets/images';
import { HeartRateIcon, HeartSqure } from '../../../assets/svgs';
type Props = NativeStackScreenProps<StackParamList, 'BPDetails'>;

const BPDetails: React.FC<Props> = ({ navigation }) => {
  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainerStyle}
      >
        <View style={[styles.infoContainer, { paddingTop: 40 }]}>
          <HeartRateIcon width={30} height={30} />
          <AppText style={styles.largeTitle}>138/87</AppText>
          <AppText style={[styles.title, { color: colors.grey_30 }]}>
            mmHg
          </AppText>
        </View>
        <View style={[styles.infoContainer, { marginTop: -30 }]}>
          <HeartSqure width={30} height={30} />
          <AppText style={styles.largeTitle}>82</AppText>
          <AppText style={[styles.title, { color: colors.grey_30 }]}>
            mmHg
          </AppText>
        </View>

        <DiabetesIndicator
          label="Hypertension stade 1"
          unit="2023 ACC/AHA"
          containerStyle={styles.indicatorContainer}
        />
        {/* BLOOD GLUCOSE CHART */}
        <View style={styles.chartContainer}>
          <Image
            source={images.BPChartImage}
            resizeMode="contain"
            style={{ height: 250, width: '100%' }}
          />
        </View>
        {/* Informational section with recommendation */}
        <InfoCard
          title="Analysis Blood Glucose"
          description="It’s important to note that individual variations can occur, and what’s considered normal can differ slightly based on factors such as age, gender and overall health. With your provided data, we are pleased to inform you that you are having a good health."
          actionText="Continue monitoring your blood Glucose level periodically, ensuring you maintain a balanced diet and regular physical activity to keep these readings stable."
        />

        {/* Static disclaimer section */}
        <InfoCard
          title="Disclaimer"
          description="The device is for informational purposes only and should not be used for medical purpose"
        />

        {/* Row with section title and "See all" action */}
        <View style={styles.rowContainer}>
          <AppText style={styles.measurementTitle}>Helpful for you</AppText>
          <TouchableOpacity activeOpacity={0.5}>
            <AppText style={styles.seeAllText}>See all</AppText>
          </TouchableOpacity>
        </View>

        {/* Scrollable health tips card list */}
        <View>
          <HealthTipsList />
        </View>
      </ScrollView>
    </View>
  );
};

export default BPDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  contentContainerStyle: {
    paddingBottom: 34,
  },
  rowContainer: {
    ...appStyles.flexBtw,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 34,
  },
  measurementTitle: {
    ...appStyles.h3,
    color: colors.grey_100,
  },
  seeAllText: {
    ...appStyles.body2,
    fontSize: 12,
    color: colors.primary,
  },
  chartContainer: {
    backgroundColor: colors.white,
    marginHorizontal: sizes.paddingHorizontal,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 34,
    paddingLeft: 10,
  },
  title: {
    fontSize: 20,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Medium,
  },
  largeTitle: {
    fontSize: 70,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Bold,
  },
  infoContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    alignSelf: 'center',
    gap: 10,
  },
  indicatorContainer: {
    marginTop: 16,
    marginHorizontal: sizes.paddingHorizontal,
  },
});
