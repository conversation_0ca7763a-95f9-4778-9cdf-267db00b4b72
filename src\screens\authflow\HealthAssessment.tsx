import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { useEffect, useState } from 'react';
import { colors, fonts, sizes } from '../../theme/theme';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../navigations/StackNavigator';
import { AnimatedProgressBar, AppText, HeaderLeft } from '../../componets';
import GenderStep from '../../componets/ssessmentComp/GenderStep';
import AgeStep from '../../componets/ssessmentComp/AgeStep';
import WeightStep from '../../componets/ssessmentComp/WeightStep';
import HeightStep from '../../componets/ssessmentComp/HeightStep';
import BMIBox from '../../componets/ssessmentComp/BMIBox';
import ConditionsToManageSteps from '../../componets/ssessmentComp/ConditionsToManageSteps';
import DiabetesType from '../../componets/ssessmentComp/DiabetesType';
import DiagnosedStep from '../../componets/ssessmentComp/DiagnosedStep';
import MedicationStatusStep from '../../componets/ssessmentComp/MedicationStatusStep';
import MedicationTypeStep from '../../componets/ssessmentComp/MedicationTypeStep';
import GlycemicMonitoringStep from '../../componets/ssessmentComp/GlycemicMonitoringStep';
import HypertensionStatusStep from '../../componets/ssessmentComp/HypertensionStatusStep';
import HypertensionSinceStep from '../../componets/ssessmentComp/HypertensionSinceStep';
import ComorbidityStep from '../../componets/ssessmentComp/ComorbidityStep';
import BloodPressureStep from '../../componets/ssessmentComp/BloodPressureStep';
import StressLevelStep from '../../componets/ssessmentComp/StressLevelStep';
import EmotionStep from '../../componets/ssessmentComp/EmotionStep';
import SleepQualityStep from '../../componets/ssessmentComp/SleepQualityStep';
import FallAsleepStep from '../../componets/ssessmentComp/FallAsleepStep';
import FallAsleepToNightStep from '../../componets/ssessmentComp/FallAsleepToNightStep';
import ManageDiabetesLevel from '../../componets/ssessmentComp/ManageDiabetesLevel';
import LifeCraftStep from '../../componets/ssessmentComp/LifeCraftStep';
import RemindersSteps from '../../componets/ssessmentComp/RemindersSteps';
import { useAppDispatch } from '../../redux/config/Store';
import { setUser } from '../../redux/slices/authSlice';

type Props = NativeStackScreenProps<StackParamList, 'HealthAssessment'>;
export interface GlycemicMonitoringData {
  hba1c: string;
  fastingGlucose: string;
  lastTestInterval: string;
  hba1cNotRemembered: boolean;
  lastTestMoreThanYear: boolean;
}
interface FormData {
  gender: string;
  ageRange: string;
  weight: string;
  height: string;
  bmi: string;
  conditionsToManage: string[];
  diabetesType: string;
  diabetesDiagnosedSince: string;
  medicationStatus: string;
  medicationType: [];
  glycemicMonitoring: GlycemicMonitoringData;
  hypertensionStatus: string;
  hypertensionSince: string;
  bloodPressure: {
    systolic: string;
    diastolic: string;
    pulse: string;
    bpNotRemembered: boolean;
  };
  comorbidities: [];
  stressLevel: string;
  emotion: string;
  sleepQuality: string;
  fallAsleep: string;
  fallAsToNightleep: string;
  diabetesLevel: string;
  lifeCraft: { analyzing: boolean; plan: boolean };
  reminders: { bloodPressure: boolean };
}

const HealthAssessment: React.FC<Props> = ({ navigation }) => {
  const dispatch = useAppDispatch();
  const [step, setStep] = useState(0);
  const [activeWieghtUnit, setActiveWeightUnit] = useState('Kg');
  const [activeHeightUnit, setActiveHeightUnit] = useState('cm');

  const [formData, setFormData] = useState<FormData>({
    gender: '',
    ageRange: '',
    weight: '45',
    height: activeHeightUnit === 'cm' ? '90' : '24',
    bmi: '',
    conditionsToManage: [],
    diabetesType: '',
    diabetesDiagnosedSince: '',
    medicationStatus: '',
    medicationType: [],
    glycemicMonitoring: {
      hba1c: '',
      fastingGlucose: '',
      lastTestInterval: '',
      hba1cNotRemembered: false,
      lastTestMoreThanYear: false,
    },
    hypertensionStatus: '',
    hypertensionSince: '',
    bloodPressure: {
      systolic: '',
      diastolic: '',
      pulse: '',
      bpNotRemembered: false,
    },
    comorbidities: [],
    stressLevel: '',
    emotion: '',
    sleepQuality: '',
    fallAsleep: '',
    fallAsToNightleep: '',
    diabetesLevel: '2',
    lifeCraft: { analyzing: false, plan: false },
    reminders: {
      bloodPressure: false,
    },
  });

  const updateFormData = (key: string, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };
  console.log('formData-----', formData);
  console.log('step----------', step);

  const totalSteps = Object.keys(formData).length;
  const nextStep = () => {
    if (step < totalSteps) {
      setStep(prev => prev + 1);
    }
  };
  const prevStep = () => setStep(prev => (prev === 0 ? 0 : prev - 1));

  const getHeaderTitle = () => {
    if (step <= 5) return 'Health Assessment';
    if (step <= 11) return 'Diabetes management';
    if (step <= 13) return 'Blood pressure management';
    if (step <= 14) return 'Comorbidities';
    if (step <= 19) return 'Behavioral health';
    if (step <= 21) return 'Motivation';
    if (step <= 22) return 'Reminder';

    return '';
  };
  useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        step < 21 ? (
          <TouchableOpacity
            style={styles.headerRightContainer}
            activeOpacity={0.5}
            onPress={() => {
              nextStep();
            }}
          >
            <AppText style={styles.skipButton}>Skip</AppText>
          </TouchableOpacity>
        ) : null
      ),
      headerLeft: () => <HeaderLeft onPress={() => step === 0 ?  navigation.goBack() : prevStep()} />,
      headerTitle: getHeaderTitle(),
    });
  }, [step]);

  const steps = [
    // What is your gender?
    <GenderStep
      value={formData.gender}
      onChange={val => updateFormData('gender', val)}
      onContinue={nextStep}
      onPressSkip={() => nextStep()}
    />,
    // What is your Age?
    <AgeStep
      value={formData.ageRange}
      onChange={val => updateFormData('ageRange', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // What is your weight?
    <WeightStep
      value={formData.weight}
      onChange={val => updateFormData('weight', val)}
      onContinue={nextStep}
      activeUnit={activeWieghtUnit}
      setActiveUnit={setActiveWeightUnit}
      onPressBack={prevStep}
    />,
    // What is your height?
    <HeightStep
      value={formData.height}
      onChange={val => updateFormData('height', val)}
      onContinue={nextStep}
      activeUnit={activeHeightUnit}
      setActiveUnit={setActiveHeightUnit}
      onPressBack={prevStep}
    />,
    // Your BMI is within the obese range
    <BMIBox
      weight={100.3}
      weightUnit="kg"
      height={172}
      heightUnit="cm"
      bodyFat={31}
      onContinue={nextStep}
      onChange={val => updateFormData('bmi', val)}
      onPressBack={prevStep}
    />,

    // <BMIBox
    //   weight={Number(formData.weight) || 200.3}
    //   weightUnit={(activeHeightUnit as 'kg' | 'lb') || 'kg'}
    //   height={Number(formData.height) || 100}
    //   heightUnit={activeHeightUnit === 'inches' ? 'in' : ('cm' as 'cm' | 'in')}
    //   bodyFat={31}
    // />,

    // What conditions do you want to manage?
    <ConditionsToManageSteps
      value={formData.conditionsToManage}
      onChange={val => updateFormData('conditionsToManage', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // What is your diabetes diagnosis?
    <DiabetesType
      value={formData.diabetesType}
      onChange={val => updateFormData('diabetesType', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // When were you diagnosed ?
    <DiagnosedStep
      value={formData.diabetesDiagnosedSince}
      onChange={val => updateFormData('diabetesDiagnosedSince', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // Do you take any medications?
    <MedicationStatusStep
      value={formData.medicationStatus}
      onChange={val => updateFormData('medicationStatus', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // Which type of medications do you take ?
    <MedicationTypeStep
      value={formData.medicationType}
      onChange={val => updateFormData('medicationType', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // What was your most recent HbA1c? (in %)
    <GlycemicMonitoringStep
      value={formData.glycemicMonitoring}
      onChange={val => updateFormData('glycemicMonitoring', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // Are you diagnosed with Hypertension?
    <HypertensionStatusStep
      value={formData.hypertensionStatus}
      onChange={val => updateFormData('hypertensionStatus', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // When were you diagnosed ?
    <HypertensionSinceStep
      value={formData.hypertensionSince}
      onChange={val => updateFormData('hypertensionSince', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // What was your average blood pressure measurement?
    <BloodPressureStep
      value={formData.bloodPressure}
      onChange={val => updateFormData('bloodPressure', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // What other health conditions are you managing!
    <ComorbidityStep
      value={formData.comorbidities}
      onChange={val => updateFormData('comorbidities', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // What level of stress or anxiety have you experienced over the past month?
    <StressLevelStep
      value={formData.stressLevel}
      onChange={val => updateFormData('stressLevel', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // Which of these emotions have you felt the most over the past month?
    <EmotionStep
      value={formData.emotion}
      onChange={val => updateFormData('emotion', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // How would you rate the overall quality of your sleep?
    <SleepQualityStep
      value={formData.sleepQuality}
      onChange={val => updateFormData('sleepQuality', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // Do you have trouble falling asleep?
    <FallAsleepStep
      value={formData.fallAsleep}
      onChange={val => updateFormData('fallAsleep', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // How would you like to fall asleep tonight?
    <FallAsleepToNightStep
      value={formData.fallAsToNightleep}
      onChange={val => updateFormData('fallAsToNightleep', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // How motivated are you to manage your diabetes?
    <ManageDiabetesLevel
      value={formData.diabetesLevel}
      onChange={val => updateFormData('diabetesLevel', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // Get ready for a remarkable improvement in your lifestyle.
    <LifeCraftStep
      value={formData.lifeCraft}
      onChange={val => updateFormData('lifeCraft', val)}
      onContinue={nextStep}
      onPressBack={prevStep}
    />,
    // Develop the habit of a winner
    <RemindersSteps
      value={formData.reminders}
      onChange={val => updateFormData('reminders', val)}
      onContinue={() => {
        navigation.replace('BottomNavigation');
        dispatch(setUser({ id: '1111-2222-3333-4444' }));
      }}
      onPressBack={prevStep}
    />,
  ];

  return (
    <View style={styles.container}>
      <AnimatedProgressBar
        max={Object.keys(formData).length}
        value={step + 1}
        height={8}
        fillColor={colors.primary}
        containerStyle={{ marginHorizontal: 4, marginBottom: 20 }}
      />
      {steps[step]}
    </View>
  );
};

export default HealthAssessment;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal - 4,
    paddingTop: 24,
  },
  headerRightContainer: {
    paddingHorizontal: 23,
    paddingVertical: 8,
    backgroundColor: colors.primary,
    borderRadius: 40,
  },
  skipButton: {
    fontFamily: fonts.NotoSans_Regular,
    color: colors.white,
    fontSize: 10,
  },
});
