import { StyleSheet, View } from 'react-native';
import React from 'react';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import { BPMachine } from '../../../assets/svgs';
import { AppButton, AppText } from '../../../componets';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'BPMachineDetails'>;

const BPMachineDetails: React.FC<Props> = ({ navigation }) => {
  return (
    <View style={styles.container}>
      <BPMachine style={{ alignSelf: 'center' }} />
      <AppText pt={40} style={styles.paragraph}>
        Press and hold 'MEM' key (or 'M' key) on your blood pressure monitor
        until it flashes
      </AppText>
      <AppText style={styles.paragraph}>
        'Boo' on your blood pressure monitor display.
      </AppText>

      <AppText mb={50} style={styles.title}>
        Click the ‘Scan device’ button sto start{' '}
      </AppText>
      <AppButton
        title="Scan"
        onPress={() => navigation.navigate('OnboardBPDevice')}
      />

      <AppButton
        title="Cancel"
        containerStyle={[appStyles.strokeButtonContainer, { marginTop: 18 }]}
        titleStyle={appStyles.strokeButtonText}
        onPress={() => {}}
      />
    </View>
  );
};

export default BPMachineDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 72,
  },
  paragraph: {
    fontSize: 18,
    color: '#949494',
    fontFamily: fonts.NotoSans_Regular,
    textAlign: 'center',
    paddingHorizontal: 40,
  },
  title: {
    fontSize: 18,
    color: '#333333',
    fontFamily: fonts.Catamaran_SemiBold,
    textAlign: 'center',
    paddingTop: 18,
  },
});
