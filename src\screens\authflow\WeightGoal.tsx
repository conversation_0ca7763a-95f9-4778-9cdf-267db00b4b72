import React, { useState } from 'react';
import { StyleSheet, View, Text, Dimensions } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../navigations/StackNavigator';
import { colors, fonts, sizes } from '../../theme/theme';
import { AppButton, AppText, AnimatedProgressBar } from '../../componets';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AnimatedTabs from '../../componets/common/AnimatedTabs';
import { RulerPicker } from '../../componets/rulePicker';

type Props = NativeStackScreenProps<StackParamList, 'WeightGoal'>;

const { width: screenWidth } = Dimensions.get('window');

const WeightGoal: React.FC<Props> = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  const [goalWeight, setGoalWeight] = useState('65');
  const [activeUnit, setActiveUnit] = useState('KG');

  const TABS = ['KG', 'LB'];

  // Convert weight between units
  const convertWeight = (weight: number, fromUnit: string, toUnit: string) => {
    if (fromUnit === toUnit) return weight;
    if (fromUnit === 'KG' && toUnit === 'LB') {
      return Math.round(weight * 2.20462);
    }
    if (fromUnit === 'LB' && toUnit === 'KG') {
      return Math.round(weight / 2.20462);
    }
    return weight;
  };

  // Handle unit change
  const handleUnitChange = (newUnit: string) => {
    const currentWeight = parseFloat(goalWeight);
    const convertedWeight = convertWeight(currentWeight, activeUnit, newUnit);
    setGoalWeight(convertedWeight.toString());
    setActiveUnit(newUnit);
  };

  // Calculate ideal weight range (example calculation)
  const getIdealWeightRange = () => {
    const weight = parseFloat(goalWeight);
    const lowerBound = (weight - (activeUnit === 'KG' ? 10 : 22)).toFixed(2);
    const upperBound = (weight + (activeUnit === 'KG' ? 10 : 22)).toFixed(2);
    const unit = activeUnit.toLowerCase();
    return `${lowerBound} ${unit}-${upperBound} ${unit}`;
  };

  // Calculate weight loss needed (assuming current weight is higher)
  const getWeightLoss = () => {
    const currentWeight = activeUnit === 'KG' ? 67 : 148; // This would come from user data
    const targetWeight = parseFloat(goalWeight);
    const loss = (currentWeight - targetWeight).toFixed(2);
    return loss;
  };

  const renderProgressDots = () => {
    return (
      <View style={styles.progressContainer}>
        {[0, 1, 2, 3].map(index => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index === 1 && styles.activeDot, // Second dot is active
            ]}
          />
        ))}
      </View>
    );
  };

  const renderWeightRuler = () => {
    const minWeight = activeUnit === 'KG' ? 40 : 88; // 40kg = ~88lbs
    const maxWeight = activeUnit === 'KG' ? 150 : 330; // 150kg = ~330lbs

    return (
      <View style={styles.rulerContainer}>
        <RulerPicker
          min={minWeight}
          max={maxWeight}
          step={1}
          fractionDigits={0}
          onValueChange={number => setGoalWeight(number.toString())}
          unit={activeUnit}
          indicatorColor={colors.primary}
          stepWidth={4}
          initialValue={Number(goalWeight)}
          decelerationRate={'normal'}
          gapBetweenSteps={3}
          shortStepHeight={20}
          shortStepColor="#94A3B8"
          longStepHeight={50}
          longStepColor={colors.eastBay}
          height={120}
          unitTextStyle={{
            fontSize: 16,
            color: colors.grey_60,
          }}
          valueTextStyle={{
            fontSize: 32,
            color: colors.grey_90,
            fontFamily: fonts.Catamaran_Bold,
          }}
        />
      </View>
    );
  };

  const renderRecommendationCard = () => {
    return (
      <View style={styles.recommendationCard}>
        <Text style={styles.recommendedLabel}>Recommended</Text>
        <View style={styles.recommendationRow}>
          <Text style={styles.recommendationText}>Lose body weight:</Text>
          <Text style={styles.recommendationValue}>
            {getWeightLoss()}{' '}
            <Text style={styles.recommendationUnit}>{activeUnit}</Text>
          </Text>
        </View>
        <Text style={styles.idealWeightText}>
          Ideal body weight: {getIdealWeightRange()}
        </Text>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Progress dots */}
      {renderProgressDots()}

      {/* Main content */}
      <View style={styles.content}>
        <AppText style={styles.title}>What is Your Goal Weight?</AppText>

        {/* Weight display */}
        <View style={styles.weightDisplay}>
          <Text style={styles.weightLabel}>Weight</Text>
          <Text style={styles.weightValue}>{goalWeight}</Text>
          <AnimatedTabs
            activeTab={activeUnit}
            setActiveTab={handleUnitChange}
            TABS={TABS}
            containerStyle={styles.tabsContainer}
          />
        </View>

        {/* Weight ruler */}
        {renderWeightRuler()}

        {/* Recommendation card */}
        {renderRecommendationCard()}
      </View>

      {/* Bottom buttons */}
      <View style={styles.bottomContainer}>
        <AppButton
          title="Next Step"
          onPress={() => {
            // Navigate to next screen or complete onboarding
            navigation.navigate('BottomNavigation');
          }}
          containerStyle={styles.nextButton}
        />

        {/* Bottom indicator line */}
        <View style={styles.bottomIndicator} />
      </View>
    </View>
  );
};

export default WeightGoal;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 60,
    paddingBottom: 40,
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.grey_20,
  },
  activeDot: {
    backgroundColor: colors.eastBay,
    width: 24,
    borderRadius: 12,
  },
  content: {
    flex: 1,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  title: {
    fontSize: 24,
    fontFamily: fonts.Catamaran_Bold,
    color: colors.grey_80,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 32,
  },
  weightDisplay: {
    alignItems: 'center',
    marginBottom: 40,
  },
  weightLabel: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_80,
    marginBottom: 8,
  },
  weightValue: {
    fontSize: 52,
    fontFamily: fonts.Catamaran_Bold,
    color: colors.grey_90,
    marginBottom: 16,
  },
  tabsContainer: {
    marginTop: 0,
  },
  rulerContainer: {
    height: 200,
    marginBottom: 40,
    justifyContent: 'center',
  },
  recommendationCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  recommendedLabel: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.primary,
    textAlign: 'center',
    marginBottom: 12,
  },
  recommendationRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  recommendationText: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_80,
  },
  recommendationValue: {
    fontSize: 20,
    fontFamily: fonts.Catamaran_Bold,
    color: colors.primary,
  },
  recommendationUnit: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.primary,
  },
  idealWeightText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_60,
    textAlign: 'center',
  },
  bottomContainer: {
    paddingHorizontal: sizes.paddingHorizontal,
    paddingBottom: 40,
  },
  nextButton: {
    marginBottom: 20,
  },
  bottomIndicator: {
    width: 134,
    height: 5,
    backgroundColor: colors.grey_90,
    borderRadius: 3,
    alignSelf: 'center',
  },
});
