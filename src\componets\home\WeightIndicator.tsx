import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import Svg, { Polygon } from 'react-native-svg';
import { ChevronDown, ChevronRight, ChevronUp } from '../../assets/svgs';

interface Props {
  title: string;
  isIndicator?: boolean;
  label1: string;
  value1: string;
  label2: string;
  value2: string;
  label3: string;
  value3: string;
  unit1?: string;
  unit2?: string;
  unit3?: string;
  expanded?: boolean;
  setExpanded?: (val: boolean) => void;
}

const WeightIndicator: React.FC<Props> = ({
  title,
  isIndicator = false,
  label1,
  value1,
  label2,
  value2,
  label3,
  value3,
  unit1,
  unit2,
  unit3,
  expanded,
  setExpanded,
}) => {
  const activeIndex = 1;
  const colorSegments = [
    '#8D79F6',
    '#12B76A',
    '#FEDF89',
    '#FEC84B',
    '#FDB022',
    '#F97066',
  ];

  const getBMICategory = (bmi: number) => {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 24.9) return 'Normal';
    if (bmi < 29.9) return 'Overweight';
    if (bmi < 34.9) return 'Obese';
    return 'Extremely Obese';
  };

  const renderTitleRight = () => {
    if (title === 'Weight Loss') {
      return <ChevronRight stroke={colors.primary} />;
    }

    if (title === 'Current Weight') {
      return <Text style={styles.catText}>{getBMICategory(32)}</Text>;
    }

    return (
      <TouchableOpacity style={styles.expandContainer}>
        <Text style={styles.toggleText}>5 Days</Text>
        <ChevronDown stroke={colors.primary} />
      </TouchableOpacity>
    );
  };

  const renderValueColumn = (
    label: string,
    value: string,
    unit?: string,
    isAvg?: boolean,
  ) => (
    <View style={styles.column}>
      <Text style={styles.label}>{label}</Text>
      <Text style={styles.value}>
        {value}
        {!!unit && (
          <Text
            style={[styles.unitText, isAvg ? { color: '#F97066' } : undefined]}
          >
            {'  '}
            {unit}
          </Text>
        )}
      </Text>
    </View>
  );

  const renderIndicatorBar = () => {
    if (!isIndicator) return null;

    return (
      <View>
        <View style={styles.pointerWrapper}>
          <View
            style={[
              styles.pointer,
              {
                left: `${(activeIndex + 0.5) * (100 / colorSegments.length)}%`,
              },
            ]}
          >
            <Svg height="6" width="12" viewBox="0 0 12 6">
              <Polygon points="6,6 0,0 12,0" fill="#3C465D" />
            </Svg>
          </View>
        </View>
        <View style={styles.segmentWrapper}>
          {colorSegments.map((color, index) => (
            <View
              key={index}
              style={[styles.segment, { backgroundColor: color }]}
            />
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.card}>
      <View style={appStyles.flexBtw}>
        <Text style={styles.title}>
          {title}
          {title !== 'Weight Loss' && <Text style={styles.unitText}> Kg</Text>}
        </Text>
        {renderTitleRight()}
      </View>

      <View style={styles.row}>
        {renderValueColumn(label1, value1, unit1)}
        {renderValueColumn(label2, value2, unit2, title === 'Avg')}
        {renderValueColumn(label3, value3, unit3)}
      </View>

      {renderIndicatorBar()}

      {title === 'Current Weight' && (
        <TouchableOpacity
          hitSlop={20}
          style={styles.expandContainer}
          onPress={() => setExpanded?.(!expanded)}
        >
          <Text style={styles.toggleText}>
            {expanded ? 'See less ' : 'Show more '}
          </Text>
          {expanded ? (
            <ChevronDown stroke={colors.primary} />
          ) : (
            <ChevronUp stroke={colors.primary} />
          )}
        </TouchableOpacity>
      )}
    </View>
  );
};

export default WeightIndicator;

const styles = StyleSheet.create({
  card: {
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingVertical: 10,
    paddingHorizontal: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    marginTop: 20,
    marginHorizontal: sizes.paddingHorizontal,
  },
  pointerWrapper: {
    position: 'relative',
    height: 10,
    marginTop: 12,
  },
  pointer: {
    position: 'absolute',
    marginLeft: -7, // half width of SVG
    bottom: 8,
  },
  segmentWrapper: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
  },
  segment: {
    flex: 1,
    height: 8,
    borderRadius: 8,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 6,
    marginTop: 8,
  },
  column: {
    alignItems: 'center',
  },
  label: {
    fontSize: 12,
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Regular,
  },
  value: {
    fontSize: 18,
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_SemiBold,
    lineHeight: 30,
  },
  unitText: {
    fontSize: 12,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
  },
  catText: {
    fontSize: 14,
    color: '#F97066',
    fontFamily: fonts.Catamaran_Medium,
  },
  toggleText: {
    color: colors.primary,
    textAlign: 'center',
    fontSize: 12,
    fontFamily: fonts.NotoSans_Medium,
    lineHeight: 14,
  },
  expandContainer: {
    flexDirection: 'row',
    alignSelf: 'center',
    marginTop: 10,
    gap: 8,
  },
  title: {
    fontSize: 16,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
  },
});
