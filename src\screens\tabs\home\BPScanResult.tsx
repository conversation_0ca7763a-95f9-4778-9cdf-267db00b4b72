import { StyleSheet, View } from 'react-native';
import React from 'react';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import { AppButton, AppText, HealthMetricRow } from '../../../componets';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'BPScanResult'>;

const BPScanResult: React.FC<Props> = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.container, { paddingBottom: insets.bottom }]}>
      <View>
        <AppText style={styles.title}>Verify the Values</AppText>
        <AppText pt={27} style={styles.subtitle}>
          Once the values are recognized successfully, they will appear here.
        </AppText>
        <AppText pt={14} style={styles.subtitle}>
          If the values are not correct, you can modify them before saving.
        </AppText>

        {/* Blood BMI level */}
        <HealthMetricRow
          containerStyle={{ marginTop: 16 }}
          label="Systolic"
          value={'128'}
          unit="bpm"
        />
        {/* Blood BMI level */}
        <HealthMetricRow
          containerStyle={{ marginTop: 16 }}
          label="Diastolic"
          value={'84'}
          unit="bpm"
        />
        {/* Blood Pulse level */}
        <HealthMetricRow
          containerStyle={{ marginTop: 16 }}
          label="Pulse"
          value={'79'}
          unit="bpm"
        />
      </View>
      <View>
        <AppButton title="Save" onPress={() => navigation.pop(3)} />
        <AppButton
          title="Re-Scan"
          containerStyle={[appStyles.strokeButtonContainer, { marginTop: 18 }]}
          titleStyle={appStyles.strokeButtonText}
          onPress={() => navigation.goBack()}
        />
      </View>
    </View>
  );
};

export default BPScanResult;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 24,
    justifyContent: 'space-between',
  },
  title: {
    fontSize: 18,
    color: colors.primary,
    textAlign: 'center',
    fontFamily: fonts.NotoSans_Bold,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_30,
    textAlign: 'center',
  },
});
