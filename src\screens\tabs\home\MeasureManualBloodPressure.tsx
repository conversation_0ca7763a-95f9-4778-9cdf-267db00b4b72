import { StyleSheet, Text, View } from 'react-native';
import React, { useRef, useState } from 'react';
import {
  AppButton,
  CommentBox,
  DiabetesIndicator,
  MeasurementTypeSelector,
  TagSection,
} from '../../../componets';
import { toggleTagInSections } from '../../../helper/toggleTagInSections';
import { Tags } from '../../../constants/Tags';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { colors, sizes } from '../../../theme/theme';
import { CustomRulerPicker } from '../../../componets/home/<USER>';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
type Props = NativeStackScreenProps<
  StackParamList,
  'MeasureManualBloodPressure'
>;

type TagSectionType = {
  title: string;
  tags: string[];
  selectedTags: string[];
  addButtonText?: string;
};

const MeasureManualBloodPressure: React.FC<Props> = ({ navigation }) => {
  const scrollRef = useRef<KeyboardAwareScrollView>(null);

  const [tagSections, setTagSections] = useState<TagSectionType[]>(Tags);
  const [selectedType, setSelectedType] = useState('pre-meal');
  const [comment, setComment] = useState('');

  const toggleTag = (sectionIndex: number, tag: string) => {
    setTagSections(prevSections =>
      toggleTagInSections(prevSections, sectionIndex, tag),
    );
  };
  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        ref={scrollRef}
        enableOnAndroid
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* DiabetesIndicator */}
        <DiabetesIndicator label="Measurement Type" unit="2023 ACC/AHA" />
        {/* Systolic ruler */}
        <CustomRulerPicker
          min={45}
          max={120}
          step={1}
          fractionDigits={0}
          onValueChange={number => {}}
          unit={'mmHg'}
          title="Systolic"
          indicatorColor={colors.primary}
          decelerationRate={'normal'}
          shortStepColor="#D1D5DB"
          longStepColor={'#E5E7EB'}
          longStepHeight={64}
          shortStepHeight={24}
          height={120}
        />
        {/* Diastolic ruler */}
        <CustomRulerPicker
          min={45}
          max={120}
          step={1}
          fractionDigits={0}
          onValueChange={number => {}}
          unit={'mmHg'}
          title="Diastolic"
          indicatorColor={colors.primary}
          decelerationRate={'normal'}
          shortStepColor="#D1D5DB"
          longStepColor={'#E5E7EB'}
          longStepHeight={64}
          shortStepHeight={24}
          height={120}
        />
        {/* Heart Rate ruler */}
        <CustomRulerPicker
          min={45}
          max={120}
          step={1}
          fractionDigits={0}
          onValueChange={number => {}}
          unit={'/min'}
          title="Heart Rate"
          indicatorColor={colors.primary}
          decelerationRate={'normal'}
          shortStepColor="#D1D5DB"
          longStepColor={'#E5E7EB'}
          longStepHeight={64}
          shortStepHeight={24}
          height={120}
        />
        {/* Measurement type selector (pre/post-meal, etc.) */}
        <MeasurementTypeSelector
          selectedKey={selectedType}
          onSelect={setSelectedType}
          containerStyle={{ marginTop: 20 }}
        />
        {/* Tag section(s) for conditions */}
        {tagSections.map((section, sectionIndex) => (
          <TagSection
            key={sectionIndex}
            title={section.title}
            tags={section.tags}
            selectedTags={section.selectedTags}
            onTagToggle={tag => toggleTag(sectionIndex, tag)}
            addButtonText={section?.addButtonText || ''}
            containerStyle={{ marginTop: 32 }}
          />
        ))}
        {/* Comment BOX */}
        <CommentBox
          title="Comment"
          value={comment}
          onChangeText={setComment}
          placeholder="write here...."
          maxLength={250}
          containerStyle={{ marginTop: 32 }}
          onFocus={() => scrollRef.current?.scrollToEnd()}
        />
        {/* Save Button */}
        <AppButton
          title="Add Record"
          onPress={() => navigation.pop(2)}
          containerStyle={{ marginBottom: 18, marginTop: 32 }}
        />
      </KeyboardAwareScrollView>
    </View>
  );
};

export default MeasureManualBloodPressure;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 8,
    backgroundColor: colors.background_color,
  },
});
