import { TouchableOpacity, Text, StyleSheet, ViewStyle } from 'react-native';
import { CrossStroke2 } from '../../assets/svgs';
import { colors, fonts } from '../../theme/theme';

const ToggleableTag = ({
  item,
  selected,
  onPress,
  showCross = true,
  tagContainerStyle,
}: {
  item: string;
  selected: boolean;
  onPress: () => void;
  showCross?: boolean;
  tagContainerStyle?: ViewStyle;
}) => (
  <TouchableOpacity
    activeOpacity={0.5}
    style={[styles.tag, selected && styles.selectedTag, tagContainerStyle]}
    onPress={onPress}
  >
    <Text style={[styles.tagText, selected && styles.selectedTagText]}>
      {item}
    </Text>
    {showCross && (
      <CrossStroke2 stroke={selected ? colors.white : colors.grey_60} />
    )}
  </TouchableOpacity>
);

export default ToggleableTag;

const styles = StyleSheet.create({
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e3e7eb',
    paddingHorizontal: 10,
    paddingVertical: 7,
    borderRadius: 6,
    gap: 13,
    marginRight: 10,
    marginBottom: 10,
  },
  selectedTag: {
    backgroundColor: colors.primary,
  },
  tagText: {
    fontSize: 12,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
  },
  selectedTagText: {
    color: colors.white,
  },
});
