import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';
import { colors } from '../../theme/theme';

interface PaginationDotsProps {
  count: number;
  currentIndex: number;
}

const PaginationDots: React.FC<PaginationDotsProps> = ({
  count,
  currentIndex,
}) => {
  // Create refs for animated values
  const animatedWidths = useRef<Animated.Value[]>([]).current;

  if (animatedWidths.length !== count) {
    for (let i = 0; i < count; i++) {
      animatedWidths[i] = new Animated.Value(i === currentIndex ? 24 : 8);
    }
  }

  useEffect(() => {
    animatedWidths.forEach((animVal, index) => {
      Animated.timing(animVal, {
        toValue: index === currentIndex ? 24 : 8,
        duration: 200,
        useNativeDriver: false,
      }).start();
    });
  }, [currentIndex]);

  return (
    <View style={styles.container}>
      {animatedWidths.map((widthAnim, index) => (
        <Animated.View
          key={index}
          style={[
            styles.dot,
            {
              width: widthAnim,
              opacity: index === currentIndex ? 1 : 0.3,
              backgroundColor: colors.primary,
            },
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    marginBottom: 30,
    alignItems: 'center',
    justifyContent: 'center',
  },
  dot: {
    height: 8,
    borderRadius: 4,
    marginHorizontal: 4,
  },
});

export default PaginationDots;
