import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../navigations/StackNavigator';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { AppButton, AppText } from '../../componets';
type Props = NativeStackScreenProps<StackParamList, 'AccountCreationFeedback'>;

const AccountCreationFeedback: React.FC<Props> = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.container, { paddingBottom: insets.bottom + 64 }]}>
      <View />
      <View>
        <AppText style={styles.italicTitle}>Congratulations! </AppText>
        <AppText pt={12} style={appStyles.h2}>
          Account Created!
        </AppText>
        <AppText pt={6} style={appStyles.body1}>
          Your account had been created successfully. Please login to use your
          account and enjoy
        </AppText>
      </View>
      <AppButton title="Login" onPress={() => navigation.replace('Login')} />
    </View>
  );
};

export default AccountCreationFeedback;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    justifyContent: 'space-between',
    paddingHorizontal: sizes.paddingHorizontal,
  },
  italicTitle: {
    fontSize: 32,
    fontFamily: fonts.NotoSans_MediumItalic,
    color: '#09871D',
    textAlign: 'center',
    fontWeight: '500',
  },
});
