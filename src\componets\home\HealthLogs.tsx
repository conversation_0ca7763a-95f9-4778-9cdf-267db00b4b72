import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import AppText from '../common/AppText';
import {
  HandFill,
  HeartCircle,
  HeartFill,
  HeartRateIcon,
  HeartSqure,
  HeartStroke,
  WatchFill,
} from '../../assets/svgs';
import AppButton from '../common/AppButton';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface BloodGlucoseEntryType {
  id: string;
  value: number;
  tags: string[];
  recordedAt: string;
  status: 'Normal' | 'High' | 'Low';
  title: string;
  isBloodPressure: boolean;
  onPressLogs?: () => void;
}

interface Props {
  data: BloodGlucoseEntryType[];
  isBloodPressure?: boolean;
  containerStyle?: ViewStyle;
  onPressButton?: () => void;
  onPressLogs?: () => void;
  isWeight?: boolean;
}

const HealthLogs: React.FC<Props> = ({
  data,
  isBloodPressure,
  containerStyle,
  onPressButton,
  onPressLogs,
  isWeight,
}) => {
  const insets = useSafeAreaInsets();

  const renderItem = ({ item }: { item: BloodGlucoseEntryType }) => {
    return (
      <TouchableOpacity
        activeOpacity={0.5}
        onPress={onPressLogs}
        style={styles.entryContainer}
      >
        <View
          style={[
            styles.iconContainer,
            {
              backgroundColor: isBloodPressure
                ? '#fbdfe1'
                : isWeight
                ? '#9ADBF233'
                : '#cee3f0',
            },
          ]}
        >
          {isBloodPressure ? (
            <HeartRateIcon />
          ) : isWeight ? (
            <WatchFill width={20} height={20} />
          ) : (
            <HandFill width={24} height={24} />
          )}
        </View>
        <View style={{ flex: 1 }}>
          <View style={appStyles.flexBtw}>
            <AppText style={styles.entryTitle}>{item.title}</AppText>
            <AppText style={styles.normal}>{item.status}</AppText>
          </View>
          <View style={[appStyles.flexRow, { gap: 12 }]}>
            <AppText style={styles.entryDetails}>
              {item.value}{' '}
              {isBloodPressure ? <AppText style={[styles.unitText, {color: colors.black}]}>
             sys/Dis </AppText> : null
          }
          <AppText style={styles.unitText}>
                {isBloodPressure ? 'mmHg' : isWeight ? 'KG' : 'mmol/L'}
              </AppText>
            </AppText>
            {isWeight && (
              <AppText style={styles.entryDetails}>
                32.1 <AppText style={styles.unitText}>BMI</AppText>
              </AppText>
            )}
          </View>

          <View style={styles.entryTags}>
            {item.tags.map((tag, index) => (
              <Text key={index} style={styles.tag}>
                {tag}
              </Text>
            ))}
          </View>
          <AppText style={styles.date}>
            Recorded at:{' '}
            <AppText style={styles.dateText}>{item.recordedAt}</AppText>
          </AppText>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <View style={styles.flex}>
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={item => item.id}
          contentContainerStyle={{
            marginHorizontal: 4,
            marginBottom: 2,
          }}
        />
      </View>
      <AppButton
        title="Add New Record"
        containerStyle={{ marginBottom: insets.bottom + 20 }}
        onPress={onPressButton}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    marginHorizontal: sizes.paddingHorizontal - 4,
  },
  flex: {
    flex: 1,
  },
  entryContainer: {
    padding: 8,
    borderRadius: 12,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    height: '100%',
    justifyContent: 'center',
    width: 30,
    alignItems: 'center',
    borderRadius: 8,
  },
  entryTitle: {
    fontSize: 12,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_90,
  },
  normal: {
    backgroundColor: '#D8F7EA',
    borderWidth: 1,
    borderColor: '#31AA7A',
    fontFamily: fonts.NotoSans_Regular,
    fontSize: 10,
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 5,
  },
  entryDetails: {
    fontSize: 16,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Bold,
    lineHeight: 22,
  },
  unitText: {
    fontSize: 10,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_50,
    paddingLeft: 4,
  },
  entryTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 4,
    gap: 4,
  },
  tag: {
    backgroundColor: colors.grey_10,
    borderRadius: 4,
    fontSize: 10,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
    paddingHorizontal: 4,
    paddingVertical: 2,
  },
  date: {
    fontSize: 10,
    color: colors.grey_50,
    marginTop: 8,
    fontFamily: fonts.NotoSans_Medium,
  },
  dateText: {
    fontSize: 12,
    color: colors.grey_90,
  },
});

export default HealthLogs;
