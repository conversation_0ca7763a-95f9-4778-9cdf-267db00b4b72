import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  TouchableWithoutFeedback,
} from 'react-native';
import AppButton from '../common/AppButton';
import { appStyles, colors, fonts } from '../../theme/theme';
import { CircleButton } from '../../assets/svgs';

interface CancelPairingModalProps {
  visible: boolean;
  onCancel: () => void;
  onClose: () => void;
}

const CancelPairingModal: React.FC<CancelPairingModalProps> = ({
  visible,
  onCancel,
  onClose,
}) => {
  return (
    <Modal transparent visible={visible} animationType="fade">
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback>
            <View style={styles.content}>
              <CircleButton style={{ alignSelf: 'center' }} />
              <Text style={styles.title}>
                Do you really want to stop the pairing?
              </Text>
              <Text style={styles.message}>
                If you stop, you'll have to restart the process.
              </Text>
              <AppButton
                title="No, continue"
                onPress={onClose}
                containerStyle={{ marginTop: 24 }}
              />
              <AppButton
                title="Yes, cancel"
                containerStyle={[
                  appStyles.strokeButtonContainer,
                  { marginTop: 18, borderColor: '#0A0D120D' },
                ]}
                titleStyle={[appStyles.strokeButtonText, { color: '#3A3A3A' }]}
                onPress={onCancel}
              />
              {/* 
              <TouchableOpacity style={styles.primaryButton} onPress={onClose}>
                <Text style={styles.primaryText}>No, continue</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.secondaryButton}
                onPress={onCancel}
              >
                <Text style={styles.secondaryText}>Yes, cancel</Text>
              </TouchableOpacity> */}
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default CancelPairingModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 20,
  },
  title: {
    fontSize: 18,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    textAlign: 'center',
    paddingTop: 16,
  },
  message: {
    textAlign: 'center',
    fontFamily: fonts.NotoSans_Regular,
    fontSize: 14,
    color: colors.grey_60,
  },
  primaryButton: {
    backgroundColor: '#007BFF',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    marginBottom: 10,
    width: '100%',
  },
  primaryText: {
    color: 'white',
    fontWeight: 'bold',
  },
  secondaryButton: {
    borderWidth: 1,
    borderColor: '#007BFF',
    padding: 12,
    borderRadius: 6,
    alignItems: 'center',
    width: '100%',
  },
  secondaryText: {
    color: '#007BFF',
  },
});
