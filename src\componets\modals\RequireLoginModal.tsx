import React from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TouchableWithoutFeedback,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import AppButton from '../common/AppButton';
import { appStyles, colors, fonts } from '../../theme/theme';

interface RequireLoginModalProps {
  visible: boolean;
  onClose: () => void;
}

const RequireLoginModal: React.FC<RequireLoginModalProps> = ({
  visible,
  onClose,
}) => {
  const navigation = useNavigation();

  const handleLogin = () => {
    onClose();
    navigation.navigate('Login' as never);
  };

  const handleSignup = () => {
    onClose();
    navigation.navigate('PhoneAuth' as never);
  };

  return (
    <Modal transparent visible={visible} animationType="fade">
      <TouchableWithoutFeedback onPress={onClose}>
        <View style={styles.overlay}>
          <TouchableWithoutFeedback onPress={() => {}}>
            <View style={styles.modalContainer}>
              <Text style={styles.title}>Authentication Required</Text>
              <Text style={styles.message}>
                Please login or sign up to perform this action.
              </Text>

              <View style={styles.buttonContainer}>
                <AppButton
                  title="Login"
                  onPress={handleLogin}
                  containerStyle={{ width: '42%' }}
                />
                <AppButton
                  title="Sign Up"
                  onPress={handleSignup}
                  containerStyle={[
                    appStyles.strokeButtonContainer,
                    { width: '42%' },
                  ]}
                  titleStyle={appStyles.strokeButtonText}
                />
              </View>

              <TouchableOpacity hitSlop={10} onPress={onClose}>
                <Text style={styles.cancelText}>Cancel</Text>
              </TouchableOpacity>
            </View>
          </TouchableWithoutFeedback>
        </View>
      </TouchableWithoutFeedback>
    </Modal>
  );
};

export default RequireLoginModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 20,
    width: '85%',
    alignItems: 'center',
  },
  title: {
    fontSize: 18,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Bold,
  },
  message: {
    fontSize: 14,
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_Regular,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 23,
  },

  cancelText: {
    marginTop: 16,
    color: '#999',
    fontFamily: fonts.NotoSans_Medium,
    fontSize: 14,
  },
});
