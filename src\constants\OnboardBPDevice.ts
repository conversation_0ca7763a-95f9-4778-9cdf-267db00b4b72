import { images } from '../assets/images';

export const OnboardBPDeviceSlide = [
  {
    id: 1,
    title: 'Insert the batteries',
    description:
      'If the batteries have already been inserted and the screen is off, press the button for 5 seconds to access the device settings.',
    icon: images.BPOnboard01,
    buttonText: 'Next',
  },
  {
    id: 2,
    title: 'Set the time format',
    description:
      'Select the desired time format using the arrow keys, then confirm with the button.',
    icon: images.BPOnboard02,
    buttonText: 'Next',
  },
  {
    id: 3,
    title: 'Set the year',
    description:
      'Select the year using the arrow keys and confirm with the button.',
    icon: images.BPOnboard03,
    buttonText: 'Next',
  },
  {
    id: 4,
    title: 'Set the date and time',
    description:
      'Select the month, day, hours and minutes using the arrow keys and confirm with the button. If 12h format is selected, the display order of day and month is reversed.',
    icon: images.BPOnboard04,
    buttonText: 'Next',
  },
  {
    id: 5,
    title: 'Enable Bluetooth®',
    description:
      "Automatic data transfer via Bluetooth® is enabled—if 'bt' is blinking, confirm with the button. If disabled, enable it with the arrow keys and confirm.",
    icon: images.BPOnboard05,
    buttonText: 'Next',
  },
  {
    id: 6,
    title: 'Set user memory',
    description:
      'Set the user memory using the switch located on the right side.',
    icon: images.BPOnboard06,
    buttonText: 'Next',
  },
];
