import { StyleSheet, ScrollView, TouchableOpacity, View } from 'react-native';
import React, { useState } from 'react';
import { appStyles, colors, sizes } from '../../../theme/theme';
import {
  AppButton,
  AppText,
  DiabetesIndicator,
  HealthMetricRow,
  MeasurementDate,
  MeasurementTypeSelector,
  TagSection,
} from '../../../componets';
import { Tags } from '../../../constants/Tags';
import { toggleTagInSections } from '../../../helper/toggleTagInSections';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'MeasureBloodGlucose'>;

// Define type for tag sections
type TagSectionType = {
  title: string;
  tags: string[];
  selectedTags: string[];
  addButtonText?: string;
};

const MeasureBloodGlucose: React.FC<Props> = ({ navigation }) => {
  const [selectedType, setSelectedType] = useState('pre-meal');
  const [tagSections, setTagSections] = useState<TagSectionType[]>(Tags);

  // Toggle tag selection state in a specific section
  const toggleTag = (sectionIndex: number, tag: string) => {
    setTagSections(prevSections =>
      toggleTagInSections(prevSections, sectionIndex, tag),
    );
  };
  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Measurement Date and Time Picker */}
        <MeasurementDate
          label="Measurement Date"
          date="24/12/2024"
          time="10:24 AM"
          onPress={() => {
            // open date/time picker modal
          }}
        />

        {/* Diabetes classification indicator */}
        <DiabetesIndicator
          label="Pre Diabetes"
          unit="2023 ACC/AHA"
          containerStyle={{ marginTop: 32 }}
        />

        {/* Blood glucose level */}
        <HealthMetricRow
          containerStyle={{ marginTop: 24 }}
          label="Glycemie"
          value={'128'}
          unit="mg/dl"
        />

        {/* Ketone level */}
        <HealthMetricRow label="Ketone" value={'124'} unit="mg/dl" />

        {/* Measurement type selector (pre/post-meal, etc.) */}
        <MeasurementTypeSelector
          selectedKey={selectedType}
          onSelect={setSelectedType}
          containerStyle={{ marginTop: 20 }}
        />

        {/* Tag section(s) for conditions */}
        {tagSections.map((section, sectionIndex) => (
          <TagSection
            key={sectionIndex}
            title={section.title}
            tags={section.tags}
            selectedTags={section.selectedTags}
            onTagToggle={tag => toggleTag(sectionIndex, tag)}
            addButtonText={section?.addButtonText || ''}
            containerStyle={{ marginTop: 32 }}
          />
        ))}

        {/* Save Button */}
        <AppButton
          title="Save"
          onPress={() => navigation.pop(3)}
          containerStyle={{ marginBottom: 18, marginTop: 32 }}
        />

        {/* Re-Scan Button */}
        <TouchableOpacity
          style={appStyles.strokeButtonContainer}
          onPress={() => navigation.goBack()}
          activeOpacity={0.7}
        >
          <AppText style={appStyles.strokeButtonText}>Re-Scan</AppText>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default MeasureBloodGlucose;

const styles = StyleSheet.create({
  scrollContent: {
    paddingBottom: 40,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 8,
  },
});
