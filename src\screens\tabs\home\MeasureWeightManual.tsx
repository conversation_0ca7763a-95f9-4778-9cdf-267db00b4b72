import { <PERSON><PERSON>View, StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import ButtonwithIcon from '../../../componets/common/BittonWithIcon';
import { QuestionMarkCircle } from '../../../assets/svgs';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import { CustomRulerPicker } from '../../../componets/home/<USER>';
import RowCard from '../../../componets/home/<USER>';
import { Tags } from '../../../constants/Tags';
import { AppButton, TagSection } from '../../../componets';
import { toggleTagInSections } from '../../../helper/toggleTagInSections';
import { StackParamList } from '../../../navigations/StackNavigator';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
type Props = NativeStackScreenProps<StackParamList, 'MeasureWeightManual'>;
// Define type for tag sections
type TagSectionType = {
  title: string;
  tags: string[];
  selectedTags: string[];
  addButtonText?: string;
};
const MeasureWeightManual: React.FC<Props> = ({ navigation }) => {
  const [showKetone, setShowKetone] = useState(false);
  const [tagSections, setTagSections] = useState<TagSectionType[]>(Tags);
  // Toggle tag selection state in a specific section
  const toggleTag = (sectionIndex: number, tag: string) => {
    setTagSections(prevSections =>
      toggleTagInSections(prevSections, sectionIndex, tag),
    );
  };
  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{
          paddingHorizontal: sizes.paddingHorizontal,
          paddingBottom: 32,
        }}
      >
        <ButtonwithIcon
          label="How to measure"
          rightIcon={<QuestionMarkCircle />}
          labelStyle={styles.howToMeasureText}
          containerStyle={styles.howToMeasureContainer}
        />
        {/* Glycemie ruler */}
        <CustomRulerPicker
          min={45}
          max={120}
          step={1}
          fractionDigits={0}
          onValueChange={number => {}}
          unit={'mg/dl'}
          title="Glycemie"
          indicatorColor={colors.primary}
          decelerationRate={'normal'}
          shortStepColor="#D1D5DB"
          longStepColor={'#E5E7EB'}
          longStepHeight={64}
          shortStepHeight={24}
          height={140}
        />

        {showKetone ? (
          <CustomRulerPicker
            min={45}
            max={120}
            step={1}
            fractionDigits={0}
            onValueChange={number => {}}
            unit={'mg/dl'}
            title="Ketone"
            indicatorColor={colors.primary}
            decelerationRate={'normal'}
            shortStepColor="#D1D5DB"
            longStepColor={'#E5E7EB'}
            longStepHeight={64}
            shortStepHeight={24}
            height={100}
          />
        ) : (
          <RowCard
            onPress={() => setShowKetone(true)}
            containerStyle={{ marginTop: 24 }}
            testID="row-card"
          />
        )}
        {tagSections.map((section, sectionIndex) => (
          <TagSection
            key={sectionIndex}
            title={'Activities Precedant la Mesure'}
            tags={section.tags}
            selectedTags={section.selectedTags}
            onTagToggle={tag => toggleTag(sectionIndex, tag)}
            addButtonText={section?.addButtonText || ''}
            containerStyle={{ marginTop: 32 }}
          />
        ))}
      </ScrollView>
      <View style={appStyles.bottomContainer}>
        <AppButton
          onPress={() => navigation.goBack()}
          title="Add Record"
          containerStyle={{ width: '100%' }}
        />
      </View>
    </View>
  );
};

export default MeasureWeightManual;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingTop: 4,
  },
  howToMeasureText: {
    fontSize: 14,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_50,
    marginRight: 4,
  },

  howToMeasureContainer: {
    justifyContent: 'flex-end',
    marginTop: 4,
    gap: 4,
  },
});
