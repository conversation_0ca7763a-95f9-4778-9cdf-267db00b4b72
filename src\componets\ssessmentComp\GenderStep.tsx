import React from 'react';
import { View, FlatList, StyleSheet } from 'react-native';
import AssessmentSelector from './AssessmentSelector';
import AppText from '../common/AppText';
import AppButton from '../common/AppButton';
import assesmentStyles from './assesmentStyles';

interface GenderSelectorProps {
  value: string | '';
  onChange: (value: string) => void;
  onContinue: () => void;
  onPressSkip: () => void;
}

const options = ['Male', 'Female', 'Other'];

const GenderStep: React.FC<GenderSelectorProps> = ({
  value,
  onChange,
  onContinue,
  onPressSkip,
}) => {
  return (
    <View style={styles.container}>
      <AppText ml={4} style={[assesmentStyles.assessmentTitle]}>
        What is your gender?
      </AppText>
      <View style={{ flex: 1 }}>
        <FlatList
          data={options}
          keyExtractor={item => item}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={assesmentStyles.contentContainer}
          renderItem={({ item }) => (
            <AssessmentSelector
              option={item}
              selected={value === item}
              onPress={() => onChange(item)}
            />
          )}
          ItemSeparatorComponent={() => <View style={{ height: 12 }} />}
        />
      </View>
      <AppButton title="Continue" onPress={onContinue} disabled={!value} />
      <AppButton
        title="Prefer to skip"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressSkip}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default GenderStep;
