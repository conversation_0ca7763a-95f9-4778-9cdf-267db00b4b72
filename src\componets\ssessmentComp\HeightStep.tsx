import { StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import { RulerPicker } from '../rulePicker';
import { colors } from '../../theme/theme';
import AppButton from '../common/AppButton';
import AnimatedTabs from '../common/AnimatedTabs';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';

interface WeightStepProps {
  value: string;
  onChange: (value: string) => void;
  onContinue: () => void;
  activeUnit: string;
  setActiveUnit: (val: string) => void;
  onPressBack: () => void;
}

const HeightStep: React.FC<WeightStepProps> = ({
  value,
  onChange,
  onContinue,
  activeUnit,
  setActiveUnit,
  onPressBack,
}) => {
  const TABS = ['cm', 'inches'];

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <AppText style={assesmentStyles.assessmentTitle}>
          What is your height?{' '}
        </AppText>
        <AnimatedTabs
          activeTab={activeUnit}
          setActiveTab={setActiveUnit}
          TABS={TABS}
          containerStyle={{ marginTop: 12 }}
        />
        <RulerPicker
          min={activeUnit === 'cm' ? 90 : 24}
          max={activeUnit === 'cm' ? 250 : 96}
          step={1}
          fractionDigits={0}
          onValueChange={number => onChange(number.toString())}
          initialValue={Number(value)}
          // onValueChangeEnd={number => ()}
          unit={activeUnit}
          indicatorColor={colors.primary}
          stepWidth={4}
          decelerationRate={'fast'}
          gapBetweenSteps={3}
          shortStepHeight={20}
          shortStepColor="#64748B"
          longStepHeight={50}
          longStepColor={colors.eastBay}
          height={170}
          unitTextStyle={{
            fontSize: 40,
            color: '#676C75',
          }}
          valueTextStyle={{
            fontSize: 52,
          }}
        />
      </View>
      <AppButton title="Continue" onPress={onContinue} disabled={!value} />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

export default HeightStep;

const styles = StyleSheet.create({});
