import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { colors, sizes } from '../../../theme/theme';
import { SettingItem } from '../../../componets'; // Reusable setting row component
import {
  AboutSetting,
  EyeOff,
  PolicyStroke,
  QuestionMark,
} from '../../../assets/svgs';
type Props = NativeStackScreenProps<StackParamList, 'AccountSettings'>;

const AccountSettings: React.FC<Props> = ({ navigation }) => {
  return (
    <View style={styles.container}>
      {/* Account-related settings */}
      <SettingItem
        label="About"
        icon={<AboutSetting width={18} height={18} />}
        onPressItem={() => navigation.navigate('About')}
      />

      <SettingItem
        label="Change password"
        icon={<EyeOff stroke={colors.grey_30} />}
        onPressItem={() => navigation.navigate('ChangePassword')}
      />

      {/* Legal information section */}
      <SettingItem
        label="Terms & conditions"
        icon={<QuestionMark />}
        onPressItem={() => navigation.navigate('TermsAndConditions')}
      />

      <SettingItem
        label="Privacy policy"
        icon={<PolicyStroke />}
        onPressItem={() => navigation.navigate('PrivacyPolicy')}
      />

      {/* Preferences section */}
      <SettingItem
        label="Language"
        icon={<AboutSetting width={18} height={18} />}
        containerStyle={{ marginTop: 32 }} // Adds visual separation
      />

      <SettingItem label="Units" icon={<EyeOff stroke={colors.grey_30} />} />

      <SettingItem label="Widget" icon={<QuestionMark />} />

      {/* Miscellaneous */}
      <SettingItem label="Share VitaeChek" icon={<PolicyStroke />} />
    </View>
  );
};

export default AccountSettings;

// Screen styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 18,
  },
});
