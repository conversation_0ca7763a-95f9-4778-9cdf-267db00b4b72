import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  Image,
  ImageSourcePropType,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';
import AppText from './AppText';

export interface DeviceCardProps {
  deviceName: string;
  deviceImage: ImageSourcePropType;
  onPress?: () => void;
  containerStyle?: ViewStyle;
}

const DeviceCard: React.FC<DeviceCardProps> = ({
  deviceName,
  deviceImage,
  onPress,
  containerStyle,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.7}
      onPress={onPress}
      style={[styles.container, containerStyle]}
    >
      <View style={styles.imageContainer}>
        <Image source={deviceImage} style={styles.deviceImage} />
      </View>
      <AppText style={styles.deviceName}>{deviceName}</AppText>
    </TouchableOpacity>
  );
};

export default DeviceCard;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  imageContainer: {
    borderRadius: 30,
    overflow: 'hidden',
    marginRight: 16,
  },
  deviceImage: {
    width: 50,
    height: 50,
    resizeMode: 'contain',
  },
  deviceName: {
    fontSize: 16,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_100,
    flex: 1,
  },
});
