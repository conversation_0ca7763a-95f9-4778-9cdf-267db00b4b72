import React from 'react';
import {
  View,
  StyleSheet,
  StatusBar,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { colors, fonts, sizes } from '../../../theme/theme';
import { AppButton, AppText } from '../../../componets';
import DeviceCard from '../../../componets/common/DeviceCard';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { images } from '../../../assets/images';

type Props = NativeStackScreenProps<StackParamList, 'WeightDevices'>;

// Mock device data - using placeholder images for now
const devicesData = [
  {
    id: '1',
    name: 'VitaeChek P1 Pro',
    image: images.Device1,
  },
  {
    id: '2',
    name: 'VitaeChek P3 Pro',
    image: images.Device2,
  },
];

const WeightDevices: React.FC<Props> = ({ navigation, route }) => {
 

  const handleDevicePress = (deviceName: string) => {
    // Handle device selection
    console.log('Device selected:', deviceName);
  };

  const handleAddNewDevice = () => {
    navigation.navigate('ConnectWeightDevices');
  };

  const handleAddNewRecord = () => {
   
  };

  return (
    <View style={styles.container}>
      <StatusBar
        backgroundColor={colors.background_color}
        barStyle="dark-content"
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Device List */}
        <View style={styles.devicesList}>
          {devicesData.map(device => (
            <DeviceCard
              key={device.id}
              deviceName={device.name}
              deviceImage={device.image}
              onPress={() => handleDevicePress(device.name)}
            />
          ))}
        </View>

        {/* Add New Device Link */}
        <TouchableOpacity
          style={styles.addDeviceContainer}
          onPress={handleAddNewDevice}
          activeOpacity={0.7}
        >
          <AppText style={styles.addDeviceText}>Add new device</AppText>
        </TouchableOpacity>
      </ScrollView>

      {/* Bottom Button */}
      {/* <View style={styles.bottomContainer}>
        <AppButton
          title="Add New Record"
          onPress={handleAddNewRecord}
          containerStyle={styles.addRecordButton}
        />
      </View> */}
    </View>
  );
};

export default WeightDevices;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 20,
    paddingBottom: 20,
  },
  devicesList: {
    marginBottom: 32,
  },
  addDeviceContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  addDeviceText: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  bottomContainer: {
    paddingHorizontal: sizes.paddingHorizontal,
    paddingBottom: 20,
    backgroundColor: colors.background_color,
  },
  addRecordButton: {
    backgroundColor: colors.primary,
    marginBottom: 16,
  },
  bottomIndicator: {
    width: 134,
    height: 5,
    backgroundColor: colors.grey_80,
    borderRadius: 3,
    alignSelf: 'center',
  },
});
