import React from 'react';
import { ScrollView, StyleSheet, View } from 'react-native';
import { AppText, HorizontalLine } from '../../../componets';
import { colors, fonts, sizes } from '../../../theme/theme';

const HelpCenter = () => {
  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.content}
      showsVerticalScrollIndicator={false}
    >
      {/* Guide Label */}
      <AppText style={styles.guideLabel}>Guide</AppText>

      {/* Main Title */}
      <AppText style={styles.title}>Getting started with VitaeCheck</AppText>
      <HorizontalLine marginBottom={5} backgroundColor={colors.grey_20} />

      {/* Welcome Section */}
      <AppText style={styles.welcomeText}>
        Welcome to VitaeChek, your comprehensive healthcare companion! VitaeChek
        is designed to streamline and enhance healthcare experiences for
        doctors, patients, and caregivers. Whether you're tracking your health,
        managing appointments, or monitoring vital signs, <PERSON>eChe<PERSON> is here to
        empower your health journey.
      </AppText>

      {/* Download Section */}
      <AppText style={styles.sectionTitle}>
        Download and Install the App
      </AppText>
      <AppText style={styles.sectionContent}>
        For iOS Users: Visit the App Store, search for "VitaeChek," and tap
        "Get" to download the app.
        {'\n'}For Android Users: Go to the Google Play Store, search for
        "VitaeChek," and tap "Install" to download the app.
      </AppText>

      {/* Sign Up Section */}
      <AppText style={styles.sectionTitle}>Sign Up or Log In</AppText>
      <AppText style={styles.sectionContent}>
        <AppText style={styles.boldText}>Sign Up</AppText>: Open the app and
        choose "Sign Up." You can sign up using your email address, Google, or
        Apple account. Follow the prompts to create your profile.
        {'\n\n'}
        <AppText style={styles.boldText}>Log In</AppText>: If you already have
        an account, tap "Log In" and enter your credentials.
      </AppText>

      {/* Complete Profile Section */}
      <AppText style={styles.sectionTitle}>Complete Your Profile</AppText>
      <AppText style={styles.sectionContent}>
        After signing up, you'll be prompted to complete your profile. Provide
        details such as your name, professional background, areas of interest,
        and goals.
        {'\n'}Upload a profile picture to personalize your account and make a
        great first impression.
      </AppText>
    </ScrollView>
  );
};

export default HelpCenter;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  content: {
    padding: sizes.paddingHorizontal,
    paddingTop: 24,
    paddingBottom: 32,
  },
  guideLabel: {
    fontSize: 14,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
    marginBottom: 8,
  },
  title: {
    fontSize: 26,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    lineHeight: 32,
    marginBottom: 10,
  },
  welcomeText: {
    fontSize: 12,
    lineHeight: 20,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 14,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    marginBottom: 12,
    marginTop: 8,
  },
  sectionContent: {
    fontSize: 12,
    lineHeight: 20,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    marginBottom: 8,
  },
  boldText: {
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.grey_90,
  },
});
