import React from 'react';
import { StyleSheet, Image, View, Platform } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { colors, fonts } from '../theme/theme';
import { StackParamList } from './StackNavigator';
import { icons } from '../assets/icons';
import { Doctors, Home, Profile, Resources } from '../screens/tabs';

const Tab = createBottomTabNavigator<StackParamList>();

const BottomNavigation: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        headerShown: false,
        headerShadowVisible: true,
        tabBarInactiveTintColor: '#464C54',
        tabBarIcon: ({ focused, size }) => {
          let source;

          if (route.name === 'Home') {
            source = focused ? icons.HomeFill : icons.HomeStroke;
          } else if (route.name === 'Resources') {
            source = focused ? icons.ResourcesFill : icons.ResourcesStroke;
          } else if (route.name === 'Doctors') {
            source = focused
              ? icons.ClipboardHeartFill
              : icons.ClipboardHeartStroke;
          } else if (route.name === 'Profile') {
            source = focused ? icons.ProfileFill : icons.ProfileStroke;
          }

          return (
            <View>
              <Image
                source={source}
                style={[styles.icon, { width: size, height: size }]}
                resizeMode="contain"
              />
            </View>
          );
        },
        tabBarStyle: styles.tabBar,
        headerStyle: styles.headerStyle,
        headerTitleAlign: 'left',
        tabBarHideOnKeyboard: true,
        tabBarShowLabel: false,
        headerTitleStyle: styles.headerTitleStyle,
        tabBarItemStyle: styles.tabBarItemStyle,
      })}
    >
      <Tab.Screen name="Home" component={Home} />
      <Tab.Screen name="Resources" component={Resources} />
      <Tab.Screen name="Doctors" component={Doctors} />
      <Tab.Screen
        name="Profile"
        component={Profile}
        options={{ headerShown: true }}
      />
    </Tab.Navigator>
  );
};

export default BottomNavigation;

const styles = StyleSheet.create({
  icon: {
    resizeMode: 'contain',
  },
  tabBar: {
    backgroundColor: colors.white,
    height: Platform.OS === 'ios' ? 90 : 80,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },

  headerStyle: {
    backgroundColor: colors.background_color,
  },
  headerTitleStyle: {
    fontSize: 18,
    fontFamily: fonts.Catamaran_Bold,
    color: colors.grey_80,
  },
  tabBarItemStyle: {
    height: 70,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 15,
  },
});
