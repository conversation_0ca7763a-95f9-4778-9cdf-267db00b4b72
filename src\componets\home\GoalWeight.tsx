import { StyleSheet, Text, View } from 'react-native';
import React from 'react';
import { CustomRulerPicker } from './CustomRulerPicker';
import { appStyles, colors, fonts } from '../../theme/theme';
import AppText from '../common/AppText';

const GoalWeight = () => {
  return (
    <View>
      <View style={{}}>
        <CustomRulerPicker
          min={45}
          max={120}
          step={1}
          fractionDigits={0}
          onValueChange={number => {}}
          title={'Weight'}
          unit="KG"
          indicatorColor={colors.primary}
          decelerationRate={'normal'}
          shortStepColor="#D1D5DB"
          longStepColor={'#E5E7EB'}
          longStepHeight={64}
          shortStepHeight={24}
          height={140}
        />
      </View>
      <AppText style={styles.recommendedContainer}>Recommended</AppText>
      <View style={[appStyles.flexRow, { alignSelf: 'center' }]}>
        <AppText style={styles.paragrpah}>Lose body weight:</AppText>
        <AppText style={styles.title}>
          {'  '}2.00
          <AppText style={styles.unitText}>{'   '}KG</AppText>
        </AppText>
      </View>
      <AppText style={styles.graytext}>
        Ideal body weight: 55.37 kg~74.82 kg
      </AppText>
    </View>
  );
};

export default GoalWeight;

const styles = StyleSheet.create({
  container: {},
  recommendedContainer: {
    backgroundColor: '#9ADBF24D',
    paddingHorizontal: 14,
    borderRadius: 12,
    alignSelf: 'center',
    fontSize: 12,
    color: colors.primary,
    paddingVertical: 4,
    marginTop: 8,
  },
  paragrpah: {},
  title: {
    fontSize: 22,
    color: colors.primary,
    fontFamily: fonts.Catamaran_Bold,
    textAlign: 'center',
  },
  unitText: {
    fontSize: 10,
    fontFamily: fonts.NotoSans_Medium,
  },
  graytext: {
    fontSize: 14,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Medium,
    textAlign: 'center',
  },
});
