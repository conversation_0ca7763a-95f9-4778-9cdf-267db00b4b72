import { StyleSheet, View } from 'react-native';
import React from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { colors, sizes } from '../../../theme/theme';
import { FormInput, AppButton } from '../../../componets';
import { useFormik } from 'formik';
import * as Yup from 'yup';

type Props = NativeStackScreenProps<StackParamList, 'ChangePassword'>;

const ChangePassword: React.FC<Props> = ({ navigation }) => {
  const formik = useFormik({
    initialValues: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: '',
    },
    validationSchema: Yup.object({
      oldPassword: Yup.string().required('Old password is required'),
      newPassword: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .required('New password is required'),
      confirmPassword: Yup.string()
        .oneOf([Yup.ref('newPassword')], 'Passwords must match')
        .required('Confirm password is required'),
    }),
    onSubmit: async values => {
      console.log('Submitted:', values);
      navigation.goBack(); // or whatever your next screen is
    },
  });

  return (
    <View style={styles.container}>
      <FormInput
        label="Enter Old password"
        title="Enter Old password"
        isPassword
        value={formik.values.oldPassword}
        onChangeText={formik.handleChange('oldPassword')}
        onBlur={formik.handleBlur('oldPassword')}
        error={formik.touched.oldPassword ? formik.errors.oldPassword : ''}
      />
      <FormInput
        label="Enter new password"
        title="Enter new password"
        isPassword
        containerStyle={{ marginTop: 8 }}
        value={formik.values.newPassword}
        onChangeText={formik.handleChange('newPassword')}
        onBlur={formik.handleBlur('newPassword')}
        error={formik.touched.newPassword ? formik.errors.newPassword : ''}
      />
      <FormInput
        label="Confirm new password"
        title="Confirm new password"
        isPassword
        containerStyle={{ marginTop: 8 }}
        value={formik.values.confirmPassword}
        onChangeText={formik.handleChange('confirmPassword')}
        onBlur={formik.handleBlur('confirmPassword')}
        error={
          formik.touched.confirmPassword ? formik.errors.confirmPassword : ''
        }
      />

      <AppButton
        title="Change Password"
        onPress={formik.handleSubmit}
        containerStyle={{ marginTop: 24 }}
      />
    </View>
  );
};

export default ChangePassword;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 20,
  },
});
