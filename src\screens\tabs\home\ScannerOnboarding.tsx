import React, { useRef, useState } from 'react';
import { View, Animated, FlatList } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StackParamList } from '../../../navigations/StackNavigator';
import { AppButton } from '../../../componets';
import { onBoardStyles } from '../../../theme/theme';
import OnboardingSlide from '../../../componets/home/<USER>';
import PaginationDots from '../../../componets/home/<USER>';
import { OnboardGlucoseDevice } from '../../../constants/OnboardGlucoseDevice';

type Props = NativeStackScreenProps<StackParamList, 'ScannerOnboarding'>;

const ScannerOnboarding: React.FC<Props> = ({ navigation }) => {
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useRef(new Animated.Value(0)).current;
  const [currentIndex, setCurrentIndex] = useState(0);
  const insets = useSafeAreaInsets();

  const handleNext = () => {
    if (currentIndex < OnboardGlucoseDevice.length - 1) {
      flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
      setCurrentIndex(currentIndex + 1);
    } else {
      navigation.navigate('ScanResult');
    }
  };

  const handleScroll = (event: any) => {
    const index = Math.round(
      event.nativeEvent.contentOffset.x /
        event.nativeEvent.layoutMeasurement.width,
    );
    setCurrentIndex(index);
  };

  return (
    <View style={[onBoardStyles.container, { paddingBottom: insets.bottom }]}>
      <View style={onBoardStyles.contentContainer}>
        <Animated.FlatList
          ref={flatListRef}
          data={OnboardGlucoseDevice}
          horizontal
          pagingEnabled
          keyExtractor={item => item.id.toString()}
          renderItem={({ item }) => (
            <OnboardingSlide
              title={item.title}
              description={item.description}
              subDescription={item.subDescription}
              image={item.icon}
            />
          )}
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={handleScroll}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false },
          )}
          scrollEventThrottle={16}
        />
        <PaginationDots
          count={OnboardGlucoseDevice.length}
          currentIndex={currentIndex}
        />
      </View>

      <AppButton
        title={OnboardGlucoseDevice[currentIndex].buttonText}
        onPress={handleNext}
        containerStyle={onBoardStyles.nextButton}
      />
    </View>
  );
};

export default ScannerOnboarding;
