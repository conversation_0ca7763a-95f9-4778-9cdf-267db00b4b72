import { View, TouchableOpacity, StyleSheet, ViewStyle } from 'react-native';
import AppText from './AppText';
import ToggleableTag from './ToggleableTag';
import { colors, fonts } from '../../theme/theme';

const TagSection: React.FC<{
  title: string;
  tags: string[];
  selectedTags: string[];
  onTagToggle: (tag: string) => void;
  addButtonText: string;
  containerStyle?: ViewStyle;
}> = ({
  title,
  tags,
  selectedTags,
  onTagToggle,
  addButtonText,
  containerStyle,
}) => (
  <View style={[styles.sectionContainer, containerStyle]}>
    <AppText style={styles.sectionTitle}>{title}</AppText>
    <View style={styles.tagsContainer}>
      {tags.map(tag => (
        <ToggleableTag
          key={tag}
          item={tag}
          selected={selectedTags.includes(tag)}
          onPress={() => onTagToggle(tag)}
          showCross={false}
          tagContainerStyle={styles.tag}
        />
      ))}
      <TouchableOpacity>
        <AppText style={styles.addButtonText}>{addButtonText}</AppText>
      </TouchableOpacity>
    </View>
  </View>
);

const styles = StyleSheet.create({
  sectionContainer: {
    marginTop: 10,
    marginHorizontal: 3,
    borderRadius: 12,
    paddingHorizontal: 10,
    paddingVertical: 5,
    backgroundColor: colors.background_color,

    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_SemiBold,
    color: colors.grey_90,
    marginBottom: 10,
    marginTop: 5,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4,
  },
  tag: {
    marginRight: 0,
    marginBottom: 8,
    borderRadius: 3,
  },

  addButtonText: {
    padding: 7,
    fontSize: 12,
    borderColor: colors.grey_20,
    color: colors.grey_60,
    borderWidth: 1,
    borderRadius: 3,
  },
});

export default TagSection;
