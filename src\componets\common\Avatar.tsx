import { StyleSheet, TouchableOpacity, View, ViewStyle } from 'react-native';
import React from 'react';
import { icons } from '../../assets/icons';
import FastImage from 'react-native-fast-image';
import { images } from '../../assets/images';
import { colors } from '../../theme/theme';

const Avatar = ({
  uri,
  size,
  containerStyle,
  isEditable,
  onPressButton,
}: {
  uri: string;
  size: number;
  containerStyle?: ViewStyle;
  isEditable?: boolean;
  onPressButton?: () => void;
}) => {
  return (
    <View
      style={[
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          backgroundColor: '#CED7DA',
        },
        containerStyle,
      ]}
    >
      <FastImage
        style={[styles.image, { borderRadius: size / 2 }]}
        source={uri ? { uri: uri } : images.ImagePlaceholder}
      />
      {/* {isEditable ? (
        <TouchableOpacity
          onPress={onPressButton}
          activeOpacity={0.5}
          style={styles.editButton}>
          <EdittIcon width={10} height={10} />
        </TouchableOpacity>
      ) : null} */}
    </View>
  );
};

export default Avatar;

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
  },
  editButton: {
    position: 'absolute',
    right: 0,
    bottom: 10,
    width: 22,
    height: 22,
    backgroundColor: colors.grey_20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 0.5,
    borderRadius: 24,
  },
});
