import { StyleSheet } from 'react-native';
import { colors, fonts } from '../../theme/theme';

const assesmentStyles = StyleSheet.create({
  contentContainer: {
    paddingHorizontal: 4,
    paddingBottom: 16,
    paddingTop: 16,
  },
  outerBorder: {
    borderRadius: 14,
    height: 62,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  innerBorder: {
    borderWidth: 1,
    borderRadius: 13,
    height: 60,
    backgroundColor: colors.white,
    marginHorizontal: 1,
    alignItems: 'center',
    justifyContent: 'space-between',
    flexDirection: 'row',
    paddingHorizontal: 20,
  },
  optionText: {
    fontSize: 16,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    lineHeight: 20,
  },
  assessmentTitle: {
    fontFamily: fonts.Catamaran_Bold,
    fontSize: 16,
    color: colors.grey_80,
    lineHeight: 22,
  },
  skipButton: {
    marginBottom: 32,
    backgroundColor: colors.background_color,
  },
  skipButtonTitle: {
    color: colors.primary,
  },
  paragraph: {
    fontSize: 14,
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Regular,
  },
  innerContainer: {
    borderWidth: 0,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
    backgroundColor: colors.white,
    height: 50,
  },
  cardContainer: {
    borderRadius: 14,
    height: 62,
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
    backgroundColor: colors.white,
    overflow: 'hidden',
    alignItems: 'center',
  },
});

export default assesmentStyles;
