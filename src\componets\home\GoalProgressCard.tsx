import React from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import ProgressBar from './ProgressBar';
import { colors, sizes } from '../../theme/theme';
import { images } from '../../assets/images';

interface GoalProgressCardProps {
  title: string;
  startWeight: number;
  goalWeight: number;
  startDate: string;
  endDate: string;
  totalLoss: number;
  progressPercent: number;
}

const GoalProgressCard: React.FC<GoalProgressCardProps> = ({
  title,
  startWeight,
  goalWeight,
  startDate,
  endDate,
  totalLoss,
  progressPercent,
}) => {
  return (
    <View style={styles.card}>
      <View style={{ width: '100%', height: 218 }}>
        <Image
          source={images.TempImage}
          style={{ width: '100%', height: '100%' }}
        />
      </View>
      {/* <Text style={styles.title}>{title}</Text> */}

      {/* <ProgressBar
        progress={10}
        label={`${progressPercent}%`}
        startValue={`${startWeight.toFixed(2)} kg`}
        endValue={`${goalWeight.toFixed(2)} kg`}
      /> */}

      {/* <ProgressBar
  progress={dayProgress}
  label={`Day ${currentDay}`}
  startValue={startDate}
  endValue={endDate}
  color="#FFA726"
  backgroundColor="#FFF4E6"
/> */}
      {/* <WeightSlider /> */}
      {/* Stats */}
      {/* <View style={styles.statsRow}>
        <View style={styles.statBox}>
          <Text style={styles.statValue}>{totalLoss.toFixed(2)} kg</Text>
          <Text style={styles.statLabel}>Total Loss</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statValue}>{progressPercent} %</Text>
          <Text style={styles.statLabel}>Progress</Text>
        </View>
        <View style={styles.statBox}>
          <Text style={styles.statValue}>{goalWeight.toFixed(1)} kg</Text>
          <Text style={styles.statLabel}>Goal</Text>
        </View>
      </View> */}
    </View>
  );
};

export default GoalProgressCard;

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    paddingHorizontal: sizes.paddingHorizontal,
    marginTop: 24,
    paddingVertical: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#222',
  },
  progressContainer: {
    marginBottom: 16,
  },
  progressLabel: {
    position: 'absolute',
    zIndex: 1,
    top: -10,
    left: 10,
    backgroundColor: '#E3F2FD',
    paddingVertical: 4,
    paddingHorizontal: 10,
    borderRadius: 20,
  },
  progressText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  progressBar: {
    height: 10,
    backgroundColor: '#E0E0E0',
    borderRadius: 5,
  },
  progressFill: {
    height: 10,
    backgroundColor: '#2196F3',
    borderRadius: 5,
  },
  progressRange: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
  },
  weightText: {
    fontSize: 12,
    color: '#666',
  },
  dateText: {
    fontSize: 12,
    color: '#888',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  statBox: {
    flex: 1,
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#F6F9FB',
    borderRadius: 12,
    marginHorizontal: 4,
  },
  statValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  statLabel: {
    fontSize: 12,
    color: '#888',
    marginTop: 4,
  },
});
