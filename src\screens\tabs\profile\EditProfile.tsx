import React, { useEffect } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import {
  AppText,
  Avatar,
  FormInput,
  AppButton,
  ModalPicker,
} from '../../../componets';
import { colors, fonts, sizes } from '../../../theme/theme';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { EditIcon } from '../../../assets/svgs';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';

// ✅ Validation schema
const EditProfileSchema = Yup.object().shape({
  fullName: Yup.string().required('Full Name is required'),
  lastName: Yup.string().required('Last Name is required'),
  dob: Yup.string().required('DOB is required'),
  gender: Yup.string().required('Gender is required'),
  height: Yup.string().required('Height is required'),
  weight: Yup.string().required('Weight is required'),
  race: Yup.string().required('Race is required'),
  modeSelection: Yup.string().required('Mode is required'),
});

type Props = NativeStackScreenProps<StackParamList, 'EditProfile'>;

const EditProfile: React.FC<Props> = ({ route, navigation }) => {
  const { userData } = route.params || {};
  // Gender options for the modal picker
  const genderOptions = [
    { label: 'Male', value: 'male' },
    { label: 'Female', value: 'female' },
    { label: 'Other', value: 'other' },
  ];

  const formik = useFormik({
    initialValues: {
      fullName: userData?.fullName || '',
      lastName: userData?.lastName || '',
      dob: userData?.dob || '',
      gender: userData?.gender || '',
      height: userData?.height || '',
      weight: userData?.weight || '',
      race: userData?.race || '',
      modeSelection: userData?.modeSelection || '',
    },
    validationSchema: EditProfileSchema,
    enableReinitialize: true,
    onSubmit: values => {
      console.log('Profile Data:', values);
      // Navigate back or handle save logic
      navigation.goBack();
    },
  });

  const {
    handleChange,
    handleBlur,
    handleSubmit,
    values,
    errors,
    touched,
    isValid,
    dirty,
  } = formik;

  // Determine if this is edit mode or create mode
  const isEditMode = !!userData;
  const buttonTitle = isEditMode ? 'Save Changes' : 'Add User';
  const headerTitle = isEditMode ? 'Edit Profile' : 'Add User';
  const avatarAction = isEditMode ? 'Change Picture' : 'Add Picture';

  useEffect(() => {
    navigation.setOptions({
      headerTitle: headerTitle,
    });
  }, [navigation, headerTitle]);

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={styles.scrollContent}
      showsVerticalScrollIndicator={false}
      keyboardShouldPersistTaps="handled"
    >
      <View style={styles.container}>
        {/* Profile Avatar */}
        <Avatar
          size={100}
          uri={userData?.avatarUri || ''}
          containerStyle={styles.avatar}
        />

        {/* Change Picture Link */}
        <TouchableOpacity activeOpacity={0.5}>
          <AppText style={styles.changePictureText}>{avatarAction}</AppText>
        </TouchableOpacity>

        {/* Section Heading */}
        <AppText style={styles.sectionTitle}>Personal Information</AppText>

        <View style={styles.formContainer}>
          <FormInput
            label="Full Name"
            value={values.fullName}
            onChangeText={handleChange('fullName')}
            onBlur={handleBlur('fullName')}
            error={touched.fullName && errors.fullName ? errors.fullName : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="Last Name"
            value={values.lastName}
            onChangeText={handleChange('lastName')}
            onBlur={handleBlur('lastName')}
            error={touched.lastName && errors.lastName ? errors.lastName : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="DOB"
            value={values.dob}
            onChangeText={handleChange('dob')}
            onBlur={handleBlur('dob')}
            error={touched.dob && errors.dob ? errors.dob : ''}
            rightIcon={<EditIcon />}
          />
          <ModalPicker
            label="Gender"
            options={genderOptions}
            selectedValue={values.gender}
            onSelect={value => formik.setFieldValue('gender', value)}
            error={touched.gender && errors.gender ? errors.gender : ''}
          />
          <FormInput
            label="Height"
            value={values.height}
            onChangeText={handleChange('height')}
            onBlur={handleBlur('height')}
            error={touched.height && errors.height ? errors.height : ''}
            rightIcon={<EditIcon />}
          />
          <FormInput
            label="Weight"
            value={values.weight}
            onChangeText={handleChange('weight')}
            onBlur={handleBlur('weight')}
            error={touched.weight && errors.weight ? errors.weight : ''}
            rightIcon={<EditIcon />}
          />
          {/* Race/ethnicity */}
          {!isEditMode && (
            <FormInput
              label="Race/ethnicity"
              value={values.race}
              onChangeText={handleChange('race')}
              onBlur={handleBlur('race')}
              error={touched.race && errors.race ? errors.race : ''}
              rightIcon={<EditIcon />}
            />
          )}

          <FormInput
            label="Mode selection"
            value={values.modeSelection}
            onChangeText={handleChange('modeSelection')}
            onBlur={handleBlur('modeSelection')}
            error={
              touched.modeSelection && errors.modeSelection
                ? errors.modeSelection
                : ''
            }
            rightIcon={<EditIcon />}
          />
        </View>

        {/* Submit Button */}
        <AppButton
          title={buttonTitle}
          onPress={handleSubmit}
          containerStyle={styles.submitButton}
          disabled={!isValid || !dirty}
        />
      </View>
    </KeyboardAwareScrollView>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 42,
    paddingBottom: 40,
  },
  avatar: {
    alignSelf: 'center',
  },
  changePictureText: {
    paddingTop: 6,
    textAlign: 'center',
    textDecorationLine: 'underline',
    fontFamily: fonts.NotoSans_SemiBold,
    fontSize: 14,
    color: colors.primary,
  },
  sectionTitle: {
    paddingTop: 32,
    fontFamily: fonts.NotoSans_SemiBold,
    fontSize: 14,
    color: colors.grey_80,
  },
  formContainer: {
    marginTop: 14,
    gap: 14,
  },
  submitButton: {
    marginTop: 28,
  },
});
