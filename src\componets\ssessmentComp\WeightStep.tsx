import { StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import { RulerPicker } from '../rulePicker';
import { colors } from '../../theme/theme';
import AppButton from '../common/AppButton';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import AnimatedTabs from '../common/AnimatedTabs';
import AppText from '../common/AppText';
import assesmentStyles from './assesmentStyles';

interface WeightStepProps {
  value: string;
  onChange: (value: string) => void;
  onContinue: () => void;
  activeUnit: string;
  setActiveUnit: (val: string) => void;
  onPressBack: () => void;
}

const WeightStep: React.FC<WeightStepProps> = ({
  value,
  onChange,
  onContinue,
  activeUnit,
  setActiveUnit,
  onPressBack,
}) => {
  const insets = useSafeAreaInsets();
  const TABS = ['Kg', 'Ib'];

  return (
    <View style={{ flex: 1 }}>
      <View style={{ flex: 1 }}>
        <AppText style={assesmentStyles.assessmentTitle}>
          What is your weight?
        </AppText>
        <AnimatedTabs
          activeTab={activeUnit}
          setActiveTab={setActiveUnit}
          TABS={TABS}
          containerStyle={{ marginTop: 12 }}
        />
        <RulerPicker
          min={45}
          max={120}
          step={1}
          fractionDigits={0}
          onValueChange={number => onChange(number.toString())}
          // onValueChangeEnd={number => ()}
          unit={activeUnit}
          indicatorColor={colors.primary}
          stepWidth={4}
          initialValue={Number(value)}
          // indicatorHeight={100}
          // decelerationRate={'fast'}
          decelerationRate={'normal'}
          gapBetweenSteps={3}
          shortStepHeight={20}
          shortStepColor="#64748B"
          longStepHeight={50}
          longStepColor={colors.eastBay}
          height={170}
          unitTextStyle={{
            fontSize: 20,
            color: '#676C75',
          }}
          valueTextStyle={{
            fontSize: 52,
          }}
        />
      </View>
      <AppButton title="Continue" onPress={onContinue} disabled={!value} />
      <AppButton
        title="Back"
        containerStyle={assesmentStyles.skipButton}
        titleStyle={assesmentStyles.skipButtonTitle}
        onPress={onPressBack}
      />
    </View>
  );
};

export default WeightStep;

const styles = StyleSheet.create({});
