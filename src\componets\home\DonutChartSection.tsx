import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { DonutChart } from 'react-native-circular-chart';
import AppText from '../common/AppText';
import { appStyles, colors, fonts } from '../../theme/theme';
import { WatchFill } from '../../assets/svgs';

const { width } = Dimensions.get('window');
const PADDING = 20;

const DATA = [
  {
    name: 'Obese',
    value: 120,
    color: '#36BFFA',
  },
  {
    name: 'Overweight',
    value: 100,
    color: '#12B76A',
  },
  {
    name: 'Healthy',
    value: 100,
    color: '#FDB022',
  },
  {
    name: 'Underweight',
    value: 90,
    color: '#F97066',
  },
  {
    name: 'Underweight',
    value: 100,
    color: '#9ADBF233',
  },
];

const DonutChartSection = () => {
  return (
    <View style={styles.chartWrapper}>
      <DonutChart
        data={DATA}
        strokeWidth={15}
        radius={110}
        containerWidth={width - PADDING * 2}
        containerHeight={240}
        type="round"
        startAngle={140}
        endAngle={-140}
        animationType="fade"
        labelTitleStyle={{ color: 'white' }}
        labelValueStyle={{ color: 'white' }}
      />

      {/* Custom text overlay */}
      <View style={styles.centerTextContainer}>
        <AppText style={styles.title}>
          BMI 30.9 <AppText style={{ color: '#F97066' }}>Obese</AppText>
        </AppText>
        <View style={[appStyles.flexRow, { gap: 8 }]}>
          <WatchFill />
          <AppText style={styles.mainText}>100</AppText>
          <AppText style={styles.subText}>KG</AppText>
        </View>
        <View></View>
        <AppText style={styles.setGoalText}>Set Goal</AppText>
        <View style={styles.unitContainer}>
          <AppText style={styles.text}>120 kg</AppText>
          <AppText style={styles.text}>70 kg</AppText>
        </View>
      </View>
    </View>
  );
};

export default DonutChartSection;

const styles = StyleSheet.create({
  chartWrapper: {
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  centerTextContainer: {
    position: 'absolute',
    alignItems: 'center',
    justifyContent: 'center',
    top: 70,
  },
  title: {
    fontSize: 16,
    color: '#8A8D9F',
    fontFamily: fonts.NotoSans_Medium,
    marginBottom: -10,
  },
  mainText: {
    fontSize: 50,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Bold,
  },
  subText: {
    fontSize: 20,
    color: colors.grey_30,
    fontFamily: fonts.Catamaran_Medium,
  },
  setGoalText: {
    fontSize: 16,
    color: colors.primary,
    fontFamily: fonts.Catamaran_SemiBold,
    marginTop: 34,
    lineHeight: 25,
    backgroundColor: '#E8E8E7',
    paddingHorizontal: 20,
    borderRadius: 6,
    paddingVertical: 2,
  },
  text: {
    fontSize: 12,
    color: colors.grey_60,
    fontFamily: fonts.Catamaran_SemiBold,
  },
  unitContainer: {
    borderBottomWidth: 5,
    borderColor: '#D9D9D9',
    width: 170,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderRadius: 3,
    position: 'absolute',
    top: 144,
  },
});
