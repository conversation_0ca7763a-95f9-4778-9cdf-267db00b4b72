import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useState } from 'react';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import {
  AppButton,
  AppText,
  DiabetesIndicator,
  HealthMetricRow,
  MeasurementDate,
  MeasurementTypeSelector,
  TagSection,
} from '../../../componets';
import { toggleTagInSections } from '../../../helper/toggleTagInSections';
import { Tags } from '../../../constants/Tags';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'MeasureBloodGlucose'>;

// Define type for tag sections
type TagSectionType = {
  title: string;
  tags: string[];
  selectedTags: string[];
  addButtonText?: string;
};
const MeasureBloodPressure: React.FC<Props> = ({ navigation }) => {
  const [selectedType, setSelectedType] = useState('pre-meal');
  const [tagSections, setTagSections] = useState<TagSectionType[]>(Tags);

  // Toggle tag selection state in a specific section
  const toggleTag = (sectionIndex: number, tag: string) => {
    setTagSections(prevSections =>
      toggleTagInSections(prevSections, sectionIndex, tag),
    );
  };
  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        <AppText style={styles.title}>Vérifier les valeurs</AppText>
        <AppText style={styles.paragraph}>
          Une fois les valeurs reconnues avec succès, elles s'affichent. Si les
          valeurs ne sont pas correctes, vous pouvez les modifier avant
          d'enregistrer.
        </AppText>
        {/* Measurement Date and Time Picker */}
        <MeasurementDate
          label="Measurement Date"
          date="24/12/2024"
          time="10:24 AM"
          onPress={() => {
            // open date/time picker modal
          }}
          containerStyle={{ marginTop: 32 }}
        />
        {/* Diabetes classification indicator */}
        <DiabetesIndicator
          label="Pre Diabetes"
          unit="2023 ACC/AHA"
          containerStyle={{ marginTop: 32 }}
        />
        {/* Blood BMI level */}
        <HealthMetricRow
          containerStyle={{ marginTop: 16 }}
          label="Systolic"
          value={'128'}
          unit="bpm"
        />
        {/* Blood BMI level */}
        <HealthMetricRow
          containerStyle={{ marginTop: 16 }}
          label="BMI"
          value={'84'}
          unit="bpm"
        />
        {/* Blood Pulse level */}
        <HealthMetricRow
          containerStyle={{ marginTop: 16 }}
          label="Pulse"
          value={'79'}
          unit="bpm"
        />
        {/* Measurement type selector (pre/post-meal, etc.) */}
        <MeasurementTypeSelector
          selectedKey={selectedType}
          onSelect={setSelectedType}
          containerStyle={{ marginTop: 20 }}
        />
        {/* Tag section(s) for conditions */}
        {tagSections.map((section, sectionIndex) => (
          <TagSection
            key={sectionIndex}
            title={section.title}
            tags={section.tags}
            selectedTags={section.selectedTags}
            onTagToggle={tag => toggleTag(sectionIndex, tag)}
            addButtonText={section?.addButtonText || ''}
            containerStyle={{ marginTop: 32 }}
          />
        ))}

        {/* Save Button */}
        <AppButton
          title="Save"
          onPress={() => navigation.pop(5)}
          containerStyle={{ marginBottom: 18, marginTop: 32 }}
        />

        {/* Re-Scan Button */}
        <TouchableOpacity
          style={appStyles.strokeButtonContainer}
          onPress={() => navigation.pop(2)}
          activeOpacity={0.7}
        >
          <AppText style={appStyles.strokeButtonText}>Re-Scan</AppText>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default MeasureBloodPressure;

const styles = StyleSheet.create({
  scrollContent: {
    paddingBottom: 40,
  },
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  title: {
    fontSize: 20,
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_Bold,
    paddingTop: 8,
  },
  paragraph: {
    fontSize: 12,
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Regular,
    paddingTop: 7,
  },
});
