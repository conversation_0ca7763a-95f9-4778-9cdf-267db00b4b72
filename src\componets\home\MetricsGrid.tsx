import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { colors, fonts, sizes } from '../../theme/theme';
import { SquareBox, SquareGrid } from '../../assets/svgs';

const metrics = [
  { title: 'Skeletal Muscle', value: 34.5, unit: '%' },
  { title: 'Fat-Free Mass', value: 63.8, unit: 'kg' },
  { title: 'Subcutaneous Fat', value: 24.7, unit: '%' },
  { title: 'Visceral Fat', value: 11, unit: 'Level' },
  { title: 'Body Water', value: 54.3, unit: '%' },
  { title: 'Muscle Mass', value: 58.1, unit: 'kg' },
  { title: 'Bone Mass', value: 3.4, unit: 'kg' },
  { title: 'Protein', value: 19.3, unit: '%' },
  { title: 'BMR', value: 1680, unit: 'kcal' },
  { title: 'Metabolic Age', value: 36, unit: 'yrs' },
  { title: 'Sort', value: 36, unit: 'yrs' },
];

const MetricsGrid: React.FC = () => {
  const renderItem = (item: (typeof metrics)[0], index: number) => (
    <View key={index} style={styles.card}>
      {item.title === 'Sort' ? (
        <View
          style={{
            justifyContent: 'center',
            alignItems: 'center',
            flex: 1,
          }}
        >
          <SquareGrid style={{ alignSelf: 'center' }} />
          <Text style={styles.title}>{item.title}</Text>
        </View>
      ) : (
        <>
          <Text style={styles.title}>{item.title}</Text>
          <Text style={styles.value}>
            {item.value}{' '}
            <Text
              style={{
                fontSize: 12,
                color: colors.grey_60,
                fontFamily: fonts.NotoSans_Medium,
              }}
            >
              {item.unit}
            </Text>
          </Text>
          <SquareBox style={{ alignSelf: 'center', paddingTop: 2 }} />
        </>
      )}
    </View>
  );

  return <View style={styles.container}>{metrics.map(renderItem)}</View>;
};

export default MetricsGrid;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    // justifyContent: 'space-between',
    marginTop: 12,
    marginHorizontal: sizes.paddingHorizontal,
    gap: 12,
  },
  card: {
    width: sizes.width / 3 - sizes.paddingHorizontal,
    backgroundColor: colors.white,
    borderRadius: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
    padding: 10,
  },
  title: {
    fontSize: 10,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    fontStyle: 'italic',
    textAlign: 'center',
  },
  value: {
    fontSize: 14,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_SemiBold,
    textAlign: 'center',
    paddingTop: 6,
  },
});
