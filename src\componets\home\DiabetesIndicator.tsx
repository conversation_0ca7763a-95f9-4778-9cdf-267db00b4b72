import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import Svg, { Polygon } from 'react-native-svg';
import { appStyles, colors, fonts } from '../../theme/theme';
import { QuestionMark2 } from '../../assets/svgs';

const colorSegments = [
  '#8D79F6', // Purple
  '#12B76A', // Green
  '#FEDF89', // Light Yellow
  '#FEC84B', // Mid Yellow
  '#FDB022', // Darker Yellow
  '#F97066', // Red
];

interface Props {
  containerStyle?: ViewStyle;
  label: string;
  unit: string;
}

const DiabetesIndicator: React.FC<Props> = ({
  containerStyle,
  label,
  unit,
}) => {
  const activeIndex = 4;

  return (
    <View style={[styles.container, containerStyle]}>
      {/* Top Row */}
      <View style={styles.header}>
        <Text style={[styles.label, {color: colorSegments[activeIndex]}]}>{label}</Text>
        <View style={styles.rightSection}>
          <Text style={styles.source}>{unit}</Text>
          <QuestionMark2 />
        </View>
      </View>

      {/* Arrow Pointer */}
      <View style={styles.pointerWrapper}>
        <View
          style={[
            styles.pointer,
            { left: `${(activeIndex + 0.5) * (100 / colorSegments.length)}%` },
          ]}
        >
          <Svg height="6" width="12" viewBox="0 0 12 6">
            <Polygon points="6,6 0,0 12,0" fill="#3C465D" />
          </Svg>
        </View>
      </View>

      {/* Color Segments */}
      <View style={styles.segmentWrapper}>
        {colorSegments.map((color, index) => (
          <View
            key={index}
            style={[styles.segment, { backgroundColor: color }]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {},
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  label: { ...appStyles.h3 },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  source: {
    fontSize: 12,
    color: colors.grey_50,
    fontFamily: fonts.NotoSans_Regular,
  },
  questionMark: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#CBD5E0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  pointerWrapper: {
    position: 'relative',
    height: 10,
    marginTop: 10,
    marginBottom: 6,
  },
  pointer: {
    position: 'absolute',
    marginLeft: -7, // half width of SVG
  },
  segmentWrapper: {
    flexDirection: 'row',
    borderRadius: 8,
    overflow: 'hidden',
  },
  segment: {
    flex: 1,
    height: 8,
    borderRadius: 8,
  },
});

export default DiabetesIndicator;
