import React from 'react';
import {
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TextInputProps,
  View,
} from 'react-native';
import { appStyles, colors, fonts } from '../../theme/theme';
import AppText from '../common/AppText';

interface PhonePickerProps extends TextInputProps {
  countryCode?: string;
  error: string;
}

const PhonePicker: React.FC<PhonePickerProps> = ({
  countryCode = '+237',
  error,
  ...rest
}) => {
  return (
    <View>
      <View
        style={[
          styles.outerBorder,
          {
            borderColor: error ? '#FEDEDE' : colors.background_color,
          },
        ]}
      >
        <View
          style={[
            styles.container,
            {
              borderColor: error ? colors.warning : colors.grey_05,
            },
          ]}
        >
          <Text style={styles.code}>{countryCode} </Text>
          <TextInput style={styles.input} keyboardType="phone-pad" {...rest} />
        </View>
      </View>
      <AppText style={appStyles.errorText}>{error}</AppText>
    </View>
  );
};

export default PhonePicker;

const styles = StyleSheet.create({
  outerBorder: {
    borderWidth: 2,
    height: 58,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    // marginTop: 12,
  },
  container: {
    height: 54,
    borderWidth: 1,
    borderRadius: 14,
    paddingHorizontal: 16,
    backgroundColor: colors.background_color,
    borderColor: colors.grey_05,
    flexDirection: 'row',
    alignItems: 'center',
  },
  code: {
    fontSize: 14,
    color: colors.light_Secondary,
    fontFamily: fonts.NotoSans_Regular,
    paddingTop: Platform.OS == 'android' ? 4 : -2,
  },
  input: {
    flex: 1,
    fontSize: 14,
    color: colors.grey_80,
    height: 58,
  },
});
