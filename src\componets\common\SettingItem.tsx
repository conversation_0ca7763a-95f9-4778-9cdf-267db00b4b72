import {
  StyleSheet,
  Text,
  ViewStyle,
  TextStyle,
  TouchableOpacity,
} from 'react-native';
import React from 'react';
import { ChevronRight } from '../../assets/svgs';
import { colors, fonts } from '../../theme/theme';

interface Props {
  icon: React.ReactNode;
  label: string;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  onPressItem?: () => void;
  chevronColor?: string;
}

const SettingItem: React.FC<Props> = ({
  icon,
  label,
  containerStyle,
  labelStyle,
  onPressItem,
  chevronColor = colors.grey_30,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.5}
      onPress={onPressItem}
      style={[styles.container, containerStyle]}
    >
      {icon}
      <Text style={[styles.label, labelStyle]}>{label}</Text>
      <ChevronRight stroke={chevronColor} />
    </TouchableOpacity>
  );
};

export default SettingItem;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderColor: '#C6CED9',
  },
  label: {
    fontSize: 14,
    fontFamily: fonts.Catamaran_Regular,
    color: colors.grey_80,
    lineHeight: 22,
    flex: 1,
    paddingLeft: 12,
  },
});
