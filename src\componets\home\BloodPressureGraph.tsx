import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, Platform } from 'react-native';
import { LineChart } from 'react-native-gifted-charts';
import { colors, fonts, sizes } from '../../theme/theme';
import { HeartFill, HeartRateIcon } from '../../assets/svgs';
import AppText from '../common/AppText';

const BloodPressureGraph = () => {
  // Data for Systolic and Diastolic pressure
  const lineData = [
    { value: 120 },
    { value: 110 },
    { value: 100 },
    { value: 120 },
    { value: 130 },
    { value: 100 },
    { value: 120 },
    { value: 110 },
  ];

  const lineData2 = [
    { value: 160 },
    { value: 170 },
    { value: 180 },
    { value: 190 },
    { value: 160 },
    { value: 130 },
    { value: 130 },
    { value: 180 },
  ];

  const xAxisLabels = ['5 Mar', '6 Mar', '7 Mar', 'Today'];

  return (
    <View style={styles.container}>
      {/* Blood Pressure Display */}

      {/* Line Chart for Blood Pressure */}
      <View style={styles.cardContainer}>
        <View style={styles.header}>
          <HeartRateIcon width={28} height={28} />
          <Text style={styles.bloodPressure}>133/89</Text>
          <AppText>mmHg</AppText>
        </View>
        <LineChart
          data={lineData}
          data2={lineData2}
          xAxisLabelTexts={xAxisLabels}
          height={250}
          width={sizes.width * 0.68}
          curved
          noOfSections={3}
          spacing={60}
          initialSpacing={0}
          color1="#FE8282"
          color2="#FE8282"
          textColor1="green"
          dataPointsHeight={10}
          dataPointsWidth={10}
          dataPointsColor1="#FE8282"
          dataPointsColor2="#FE8282"
          dataPointsRadius1={4}
          showVerticalLines={false}
          textShiftY={-2}
          textShiftX={-5}
          textFontSize={13}
          maxValue={240}
          verticalLinesColor={'#FF5733'} // 🔸 vertical lines (e.g. orange-red)
          rulesColor={'#CFDBE9'} // 🔹 horizontal lines (e.g. blue)
          yAxisThickness={0}
          xAxisThickness={1}
          yAxisTextStyle={{
            fontSize: 12,
            color: '#3B3939',
            fontFamily: fonts.NotoSans_Medium,
          }}
          xAxisLabelTextStyle={{
            color: '#454459',
            fontSize: 12,
            marginLeft: 4,
            fontFamily: fonts.NotoSans_Regular,
          }}
          dataPointsColor={colors.primary}
        />
      </View>

      {/* Date label */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    marginTop: 34,
  },
  header: {
    alignItems: 'center',
    marginBottom: 20,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 10,
  },
  bloodPressure: {
    fontSize: 53,
    fontWeight: 'bold',
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Bold,
    lineHeight: 70,
    paddingTop: Platform.OS === 'ios' ? 10 : 0,
  },
  dateLabel: {
    marginTop: 10,
    color: '#aaa',
    fontSize: 12,
  },
  cardContainer: {
    width: sizes.width - sizes.paddingHorizontal * 2,
    backgroundColor: colors.white,
    paddingHorizontal: 10,
    paddingTop: 32,
    paddingBottom: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    borderRadius: 12,
  },
});

export default BloodPressureGraph;
