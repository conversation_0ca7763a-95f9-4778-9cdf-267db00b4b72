import { NativeStackScreenProps } from '@react-navigation/native-stack';
import React, { useState } from 'react';
import { View } from 'react-native';
import { StackParamList } from '../../../navigations/StackNavigator';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import { AppButton, AppText, CancelPairingModal } from '../../../componets';
import { CheckStroke2 } from '../../../assets/svgs';

type Props = NativeStackScreenProps<StackParamList, 'BPMonitor'>;

const BPMonitor: React.FC<Props> = ({ navigation }) => {
  const [screen, setScreen] = useState<'userSelect' | 'measurement'>(
    'userSelect',
  );
  const [showModal, setShowModal] = useState(false);

  return (
    <View
      style={[
        {
          flex: 1,
          paddingHorizontal: sizes.paddingHorizontal,
          paddingTop: 24,
          paddingBottom: 42,
        },
      ]}
    >
      {screen === 'userSelect' && (
        <View style={{ flex: 1, justifyContent: 'space-between' }}>
          <View>
            <AppText style={styles.title}>Select the memory used...</AppText>
            <AppText pv={20} style={styles.text}>
              Select the desired user memory. The device's user memory and the
              app's user memory must match for the correct values to be
              transferred from the device.
            </AppText>

            <AppButton
              title="User 1"
              containerStyle={appStyles.strokeButtonContainer}
              titleStyle={appStyles.strokeButtonText}
            />
            <AppButton
              title="User 2"
              containerStyle={[
                appStyles.strokeButtonContainer,
                { marginTop: 18 },
              ]}
              titleStyle={appStyles.strokeButtonText}
            />
          </View>

          <AppButton title="Next" onPress={() => setScreen('measurement')} />
        </View>
      )}

      {screen === 'measurement' && (
        <>
          <AppText style={styles.title}>Take the Measurement</AppText>

          <View style={[appStyles.flexRow, { gap: 8, paddingTop: 18 }]}>
            <CheckStroke2 />
            <AppText style={styles.checkText}>
              If the device is off, turn it on.
            </AppText>
          </View>
          <View style={[appStyles.flexRow, { gap: 8, paddingTop: 12 }]}>
            <CheckStroke2 />
            <AppText style={styles.checkText}>
              Place the cuff and press start to begin.
            </AppText>
          </View>
          <View style={[appStyles.flexRow, { gap: 8, paddingTop: 18 }]}>
            <CheckStroke2 />
            <AppText style={styles.checkText}>
              Transfer will begin after a successful measurement.
            </AppText>
          </View>

          <AppText pv={40} style={styles.italicText}>
            * Please do a single measurement (not a triple one)
          </AppText>

          <AppButton title="Start Measurement Now" onPress={() => navigation.navigate('MeasureBloodPressure')}/>

          <AppButton
            title="Cancel"
            containerStyle={[
              appStyles.strokeButtonContainer,
              { marginTop: 18 },
            ]}
            titleStyle={appStyles.strokeButtonText}
            onPress={() => setShowModal(true)}
          />
        </>
      )}

      <CancelPairingModal
        visible={showModal}
        onCancel={() => {
          setShowModal(false);
          navigation.navigate('MyDevices', { isBPScreen: true });
        }}
        onClose={() => setShowModal(false)}
      />
    </View>
  );
};

export default BPMonitor;

const styles = {
  title: {
    fontSize: 18,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_95,
  },
  text: {
    fontSize: 16,
    color: '#949494',
    fontFamily: fonts.NotoSans_Regular,
  },
  italicText: {
    fontSize: 14,
    color: colors.grey_40,
    fontFamily: fonts.NotoSans_Regular,
    fontStyle: 'italic',
  },
  checkText: {
    fontSize: 14,
    color: colors.grey_40,
    fontFamily: fonts.NotoSans_Regular,
  },
};
