import React from 'react';
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  ViewStyle,
  GestureResponderEvent,
  StyleProp,
  TouchableOpacityProps,
} from 'react-native';
import { PlusStroke } from '../../assets/svgs';
import { colors, fonts } from '../../theme/theme';
import AppText from '../common/AppText';

// Define prop types
type RowCardProps = {
  onPress: (event: GestureResponderEvent) => void;
  containerStyle?: StyleProp<ViewStyle>;
} & TouchableOpacityProps;

const RowCard: React.FC<RowCardProps> = ({
  onPress,
  containerStyle,
  ...restProps
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      style={[styles.container, containerStyle]}
      activeOpacity={0.7}
      {...restProps}
    >
      <AppText style={styles.label}>Ketone</AppText>
      <PlusStroke />
    </TouchableOpacity>
  );
};

export default RowCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    elevation: 1,
    padding: 8,
    borderRadius: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  label: {
    fontSize: 20,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_Medium,
  },
});
