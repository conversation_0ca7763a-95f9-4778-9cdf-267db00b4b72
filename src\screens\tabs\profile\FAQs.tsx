import React, { useState, useEffect } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { AppText, HeaderLeft, HorizontalLine } from '../../../componets';
import { colors, fonts, sizes } from '../../../theme/theme';
import { ChevronRight } from '../../../assets/svgs';

interface FAQ {
  id: number;
  question: string;
  answer: string;
}

const faqData: FAQ[] = [
  {
    id: 1,
    question: 'How do I book an appointment with a doctor using VitaeChek?',
    answer:
      'To book an appointment, go to the Doctors tab, select your preferred doctor, choose an available time slot, and confirm your booking. You will receive a confirmation notification.',
  },
  {
    id: 2,
    question:
      'Can I track my blood pressure and other health metrics in the app?',
    answer:
      'Yes, VitaeChek allows you to track various health metrics including blood pressure, heart rate, weight, and other vital signs. You can log these manually or sync with compatible devices.',
  },
  {
    id: 3,
    question: 'Is my health information secure on VitaeChek?',
    answer:
      'Absolutely. Your data is encrypted and stored securely to ensure confidentiality. Only you and authorized healthcare providers can access your personal health information.',
  },
  {
    id: 4,
    question: 'Can doctors use VitaeChek to manage multiple patients?',
    answer:
      'Yes, healthcare providers can use VitaeChek to manage multiple patients, view their health data, schedule appointments, and communicate securely with their patients.',
  },
];

const FAQItem = ({ faq, onPress }: { faq: FAQ; onPress: () => void }) => (
  <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
    <View style={styles.faqContent}>
      <AppText style={styles.questionText}>{faq.question}</AppText>
      <ChevronRight stroke={colors.grey_60} width={14} height={14} />
    </View>
    <HorizontalLine backgroundColor={colors.grey_20} />
  </TouchableOpacity>
);

const FAQs = ({ navigation }: any) => {
  const [selectedFAQ, setSelectedFAQ] = useState<FAQ | null>(null);
  const [showAnswer, setShowAnswer] = useState(false);

  useEffect(() => {
    navigation.setOptions({
      headerLeft: () => (
        <HeaderLeft
          onPress={() =>
            !showAnswer ? navigation.goBack() : handleBackPress()
          }
        />
      ),
    });
  }, [showAnswer, selectedFAQ, navigation]);

  const handleFAQPress = (faq: FAQ) => {
    setSelectedFAQ(faq);
    setShowAnswer(true);
  };

  const handleBackPress = () => {
    setSelectedFAQ(null);
    setShowAnswer(false);
  };

  if (showAnswer && selectedFAQ) {
    return (
      <View style={styles.container}>
        <ScrollView
          style={styles.scrollContainer}
          contentContainerStyle={styles.content}
          showsVerticalScrollIndicator={false}
        >
          <AppText style={styles.answerLabel}>Answer</AppText>

          <AppText style={styles.questionTitle}>{selectedFAQ.question}</AppText>

          <HorizontalLine marginBottom={20} backgroundColor={colors.grey_20} />

          <AppText style={styles.answerText}>{selectedFAQ.answer}</AppText>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollContainer}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <AppText style={styles.title}>Frequently Asked Questions</AppText>
        <HorizontalLine backgroundColor={colors.grey_20} />
        <View>
          {faqData.map(faq => (
            <FAQItem
              key={faq.id}
              faq={faq}
              onPress={() => handleFAQPress(faq)}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default FAQs;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  scrollContainer: {
    flex: 1,
  },
  content: {
    padding: sizes.paddingHorizontal,
    paddingTop: 24,
    paddingBottom: 32,
  },
  title: {
    fontSize: 26,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    lineHeight: 32,
    marginBottom: 15,
  },

  faqContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 20,
    justifyContent: 'space-between',
  },
  questionText: {
    flex: 1,
    fontSize: 12,
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_Regular,
    lineHeight: 20,
    marginRight: 12,
  },

  answerLabel: {
    fontSize: 14,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
    marginBottom: 8,
  },
  questionTitle: {
    fontSize: 22,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    lineHeight: 28,
    marginBottom: 10,
  },
  answerText: {
    fontSize: 12,
    lineHeight: 20,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
  },
});
