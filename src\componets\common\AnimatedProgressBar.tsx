import React, { useEffect, useRef } from 'react';
import { Animated, StyleSheet, View, ViewStyle } from 'react-native';
import { colors } from '../../theme/theme';

type AnimatedProgressBarProps = {
  value: number; // current fill value
  max: number; // maximum value
  height?: number;
  backgroundColor?: string;
  fillColor?: string;
  duration?: number;
  containerStyle?: ViewStyle;
};

const AnimatedProgressBar = ({
  value,
  max,
  height = 8,
  backgroundColor = '#EAEDF1',
  fillColor = colors.primary,
  duration = 500,
  containerStyle,
}: AnimatedProgressBarProps) => {
  const progress = Math.min(value / max, 1); // Clamp between 0 and 1

  const animatedValue = useRef(new Animated.Value(progress)).current;

  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: progress,
      duration,
      useNativeDriver: false,
    }).start();
  }, [progress]);

  const animatedWidth = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View
      style={[
        styles.container,
        {
          height,
          backgroundColor,
          borderRadius: height / 2,
          ...containerStyle,
        },
      ]}
    >
      <Animated.View
        style={{
          height: '100%',
          width: animatedWidth,
          backgroundColor: fillColor,
          borderRadius: height / 2,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    overflow: 'hidden',
  },
});

export default AnimatedProgressBar;
