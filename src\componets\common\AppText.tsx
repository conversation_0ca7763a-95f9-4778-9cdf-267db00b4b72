import React, { ReactNode } from 'react';
import { StyleProp, Text, TextProps, TextStyle } from 'react-native';

interface AppTextProps extends TextProps {
  children?: ReactNode;
  style?: StyleProp<TextStyle>; // ✅ Fix: Allows array of styles
  numberOfLines?: number;
  mt?: number;
  mb?: number;
  ml?: number;
  mr?: number;
  pt?: number;
  pb?: number;
  pl?: number;
  pr?: number;
  mv?: number;
  pv?: number;
  mx?: number;
  px?: number;
  ph?: number;
  color?: string;
}

const AppText: React.FC<AppTextProps> = ({
  children,
  style,
  numberOfLines,
  mt,
  mb,
  ml,
  mr,
  pt,
  pb,
  pl,
  pr,
  mv,
  pv,
  mx,
  px,
  ph,
  color,
  ...rest
}) => {
  return (
    <Text
      style={[
        {
          marginTop: mt,
          marginBottom: mb,
          marginLeft: ml ?? mx,
          marginRight: mr ?? mx,
          paddingTop: pt,
          paddingBottom: pb,
          paddingLeft: pl ?? px,
          paddingRight: pr ?? px,
          marginVertical: mv,
          paddingVertical: pv,
          paddingHorizontal: ph,
          color: color,
        },
        style,
      ]}
      numberOfLines={numberOfLines}
      {...rest}
    >
      {children}
    </Text>
  );
};

export default AppText;
