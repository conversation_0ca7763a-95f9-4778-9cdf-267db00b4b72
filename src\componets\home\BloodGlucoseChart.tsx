import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import AppText from '../common/AppText';

export interface GlucoseRange {
  label: string;
  values: string[];
  bgColor: string;
  color: string;
}

interface Header {
  title: string;
  bgColor: string;
  color: string;
}

interface BloodGlucoseChartProps {
  title: string;
  headers: Header[]; // First header is for the labels (e.g. "Mg/DL")
  data: GlucoseRange[];
}

const BloodGlucoseChart: React.FC<BloodGlucoseChartProps> = ({
  title,
  headers,
  data,
}) => {
  return (
    <View style={styles.container}>
      {/* Chart Title */}
      <AppText mb={6} style={[appStyles.h3, { lineHeight: 20 }]}>
        {title}
      </AppText>

      {/* Header Row */}
      <View style={styles.row}>
        {headers.map((header, index) => (
          <View
            key={index}
            style={[styles.cellWrapper, { backgroundColor: header.bgColor }]}
          >
            <Text
              numberOfLines={2}
              style={[styles.cell, { color: header.color }]}
            >
              {header.title}
            </Text>
          </View>
        ))}
      </View>

      {/* Data Rows */}
      {data.map((row, rowIndex) => (
        <View key={rowIndex} style={styles.row}>
          {/* Label Cell (first column) */}
          <View
            style={[
              styles.cellWrapper,
              { marginTop: 6, backgroundColor: row.bgColor },
            ]}
          >
            <Text
              style={[styles.cell, { fontStyle: 'italic', color: row.color }]}
            >
              {row.label}
            </Text>
          </View>

          {/* Value Cells */}
          {row.values.map((value, colIndex) => (
            <View
              key={colIndex}
              style={[
                styles.cellWrapper,
                { marginTop: 6, backgroundColor: row.bgColor },
              ]}
            >
              <Text style={[styles.cell, { color: row.color }]}>{value}</Text>
            </View>
          ))}
        </View>
      ))}
    </View>
  );
};

export default BloodGlucoseChart;

const styles = StyleSheet.create({
  container: {
    marginHorizontal: sizes.paddingHorizontal,
    marginTop: 34,
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  
  },
  row: {
    flexDirection: 'row',
    gap: 6,
  },
  cellWrapper: {
    // minus 48 paddingHorzontal,minus 16 card padding, minus 18 for the gap
    width: (sizes.width - 48 - 16 - 18) / 4,
    height: sizes.width * 0.08,
    backgroundColor: 'red',
    justifyContent: 'center',
    borderRadius: 6,
  },
  cell: {
    textAlign: 'center',
    fontFamily: fonts.NotoSans_Medium,
    fontSize: sizes.width * 0.024,
    // fontSize: 10,
  },
});
