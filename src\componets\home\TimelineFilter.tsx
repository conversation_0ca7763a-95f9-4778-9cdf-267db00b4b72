import {
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from 'react-native';
import React from 'react';
import { colors, fonts } from '../../theme/theme';

interface Props {
  selectedRange: string;
  setSelectedRange: (val: string) => void;
  containerStyle?: ViewStyle;
}

const TimelineFilter: React.FC<Props> = ({
  selectedRange,
  setSelectedRange,
  containerStyle,
}) => {
  const timeRanges = ['1Day', '1Week', '1Mon', '1Year'];

  return (
    <View style={[styles.rangeSelector, containerStyle]}>
      {timeRanges.map(range => (
        <TouchableOpacity
          key={range}
          onPress={() => setSelectedRange(range)}
          style={[
            styles.rangeButton,
            selectedRange === range && styles.activeButton,
          ]}
        >
          <Text
            style={[
              styles.rangeText,
              selectedRange === range && styles.activeText,
            ]}
          >
            {range}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default TimelineFilter;

const styles = StyleSheet.create({
  rangeSelector: {
    flexDirection: 'row',
    marginTop: Platform.OS === 'ios' ? 0 : 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    paddingHorizontal: 30,
    height: 40,
    alignItems: 'center',
    gap: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    marginBottom: 2,
  },
  rangeButton: {
    paddingHorizontal: 8,
    borderRadius: 6,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeButton: {
    backgroundColor: '#007aff',
  },
  rangeText: {
    fontSize: 12,
    fontFamily: fonts.NotoSans_Regular,
    lineHeight: 20,
    color: colors.grey_50,
  },
  activeText: {
    color: '#fff',
    fontSize: 12,
    fontFamily: fonts.NotoSans_Regular,
    lineHeight: 20,
  },
});
