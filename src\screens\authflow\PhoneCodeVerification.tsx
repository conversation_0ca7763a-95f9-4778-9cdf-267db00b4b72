import { StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import { AppButton, AppText, OTPInput } from '../../componets';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'PhoneCodeVerification'>;

const PhoneCodeVerification: React.FC<Props> = ({ navigation, route }) => {
  const { phone } = route?.params;
  const insets = useSafeAreaInsets();
  const [otpCode, setOtpCode] = useState('');

  const handleOTPChange = (value: string) => {
    setOtpCode(value);
  };

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={styles.scrollContent}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      <View style={[styles.container, { paddingBottom: insets.bottom + 64 }]}>
        <View>
          <AppText style={appStyles.largeTitle}>Enter Code</AppText>
          <AppText pt={10} style={styles.body}>
            Code is sent to your Email {phone}{' '}
          </AppText>
          <OTPInput length={4} onChangeOTP={handleOTPChange} />

          <AppText mt={24} style={styles.paragaph}>
            Didn’t get the code?
            <AppText style={styles.bold}> Request again</AppText>
          </AppText>
          <AppText mt={8} style={styles.paragaph}>
            Request another code in
            <AppText style={[styles.bold, { color: colors.grey_60 }]}>
              {' '}
              00:00
            </AppText>
          </AppText>
        </View>
        <AppButton
          title="Create"
          disabled={otpCode.length < 4 ? true : false}
          onPress={() => navigation.navigate('UserProfile')}
        />
      </View>
    </KeyboardAwareScrollView>
  );
};

export default PhoneCodeVerification;

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: colors.background_color,
    paddingTop: 12,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  body: {
    fontSize: 14,
    color: '#AEAEB2',
    textAlign: 'center',
    fontFamily: fonts.NotoSans_Regular,
  },
  paragaph: {
    fontFamily: fonts.NotoSans_Medium,
    fontSize: 14,
    color: colors.light_Secondary,
    textAlign: 'center',
  },
  bold: {
    fontFamily: fonts.NotoSans_Bold,
    color: colors.primary,
  },
});
