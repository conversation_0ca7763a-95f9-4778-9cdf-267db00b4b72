import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { colors, fonts, sizes } from '../../theme/theme';
import AppText from '../common/AppText';

interface Props {
  title: string;
  icon: React.ReactNode;
  containerStyle?: ViewStyle;
  onPressCard?: () => void;
}
const ActionButtons: React.FC<Props> = ({
  title,
  icon,
  containerStyle,
  onPressCard,
}) => (
  <TouchableOpacity
    onPress={onPressCard}
    activeOpacity={0.5}
    style={[styles.container, containerStyle]}
  >
    {icon}
    <AppText style={[styles.title, { fontSize: sizes.width * 0.03 }]}>
      {title}
    </AppText>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 10,
    backgroundColor: colors.grey_10,
    alignItems: 'center',
    paddingVertical: 12,
  },
  title: {
    fontSize: 12,
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_SemiBold,
    paddingTop: 8,
  },
});

export default ActionButtons;
