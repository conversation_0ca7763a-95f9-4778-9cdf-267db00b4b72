import {
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useEffect, useState } from 'react';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import TopAnimatedTab from '../../../componets/common/TopAnimatedTab';
import {
  AppText,
  BloodGlucoseChart,
  HealthLogs,
  HealthAverages,
  GlucoseLineChart,
  HealthTipsList,
  InfoCard,
  AppButton,
} from '../../../componets';
import Animated, {
  FadeInRight,
  FadeOutRight,
  LayoutAnimationConfig,
} from 'react-native-reanimated';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'BloodGlucose'>;

const BloodGlucose: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<string>('Logs'); // Track active tab state
  const TABS = ['Logs', 'Graph']; // Top tab options
 useEffect(() => {
    navigation.setOptions({
      headerRight: () => (
        activeTab ==='Logs' ? (
          <TouchableOpacity
            style={styles.headerRightContainer}
            activeOpacity={0.5}
            onPress={() => {
              // TODO: 
            }}
          >
            <AppText style={styles.shareButton}>Skip</AppText>
          </TouchableOpacity>
        ) : null
      ),
      // headerLeft: () => <HeaderLeft onPress={() => navigation.goBack()} />,
    });
  }, [activeTab]);
  // Table headers for BloodGlucoseChart
  const headers = [
    { title: 'Mg/DL', bgColor: '#E8E8E7', color: '#39434F' },
    { title: 'Fasting', bgColor: '#E8E8E7', color: '#39434F' },
    { title: 'After Eating', bgColor: '#E8E8E7', color: '#39434F' },
    { title: '2-3 hours after Eating', bgColor: '#E8E8E7', color: '#39434F' },
  ];

  // Chart data for different glucose conditions
  const data = [
    {
      label: 'Normal',
      values: ['80-100', '170-200', '120-140'],
      bgColor: '#D8F7EA',
      color: '#31AA7A',
    },
    {
      label: 'Impaired Glucose',
      values: ['101-125', '190-230', '140-160'],
      bgColor: '#FEF0C7',
      color: '#F79009',
    },
    {
      label: 'Diabetic',
      values: ['126+', '220-230', '200+'],
      bgColor: '#FECDCA',
      color: '#D92D20',
    },
  ];

  const BloodGlucoseData: any = [
    {
      id: '1',
      value: 5.2,
      tags: ['Manual measurement', 'Dinner', 'Food 10.0 grcarbs', 'Pre-meal'],
      recordedAt: '02/12/2024 at 10:30 am',
      status: 'Normal',
      title: 'Blood Glucose',
    },
  ];
  return (
    <View style={styles.container}>
      {/* System status bar style */}
      <StatusBar
        backgroundColor={colors.background_color}
        barStyle={'dark-content'}
      />

      {/* Top Tabs */}
      <View style={{ paddingHorizontal: sizes.paddingHorizontal }}>
        <TopAnimatedTab
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          TABS={TABS}
          containerStyle={{ marginTop: 12 }}
        />
      </View>
      <LayoutAnimationConfig>
        <Animated.View
          key={`tab-content-${activeTab}`}
          {...(Platform.OS === 'ios' && {
            entering: FadeInRight.springify().damping(80).stiffness(200),
            exiting: FadeOutRight.springify().damping(80).stiffness(200),
          })}
          style={[styles.tabContent]}
        >
          {activeTab === 'Logs' ? (
            <HealthLogs
              data={BloodGlucoseData}
              onPressButton={() => navigation.navigate('MeasureHealthMetrices')}
              onPressLogs={() => navigation.navigate('BloodGlucoseDetails')}
            />
          ) : (
            <><ScrollView
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{
                  backgroundColor: colors.background_color,
                  paddingBottom: 60,
                }}
              >
                <View>
                  {/* Glucose trend line graph */}
                  <GlucoseLineChart />

                  {/* Estimated A1C result */}
                  <HealthAverages />

                  {/* Static Blood Glucose range table */}
                  <BloodGlucoseChart
                    title="Blood Glucose Chart"
                    headers={headers}
                    data={data} />

                  {/* Informational section with recommendation */}
                  <InfoCard
                    title="Analysis Blood Glucose"
                    description="It’s important to note that individual variations can occur, and what’s considered normal can differ slightly based on factors such as age, gender and overall health. With your provided data, we are pleased to inform you that you are having a good health."
                    actionText="Continue monitoring your blood Glucose level periodically, ensuring you maintain a balanced diet and regular physical activity to keep these readings stable." />

                  {/* Static disclaimer section */}
                  <InfoCard
                    title="Disclaimer"
                    description="The device is for informational purposes only and should not be used for medical purpose" />

                  {/* Row with section title and "See all" action */}
                  <View style={styles.rowContainer}>
                    <AppText style={styles.measurementTitle}>
                      Helpful for you
                    </AppText>
                    <TouchableOpacity activeOpacity={0.5}>
                      <AppText style={styles.seeAllText}>See all</AppText>
                    </TouchableOpacity>
                  </View>

                  {/* Scrollable health tips card list */}
                  <HealthTipsList />
                </View>
              </ScrollView>
              {/* <View style={{ backgroundColor : colors.white}}>
                  <AppButton
                    title="Add New Record"
                    containerStyle={{ margin: 18, marginTop: 10 }}
                    onPress={() => navigation.navigate('MeasureHealthMetrices')} />
                </View> */}
                </>
          )}
        </Animated.View>
      </LayoutAnimationConfig>
    </View>
  );
};

export default BloodGlucose;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background_color,
    flex: 1,
  },
  rowContainer: {
    ...appStyles.flexBtw,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 34,
  },
  measurementTitle: {
    ...appStyles.h3,
    color: colors.grey_100,
  },
  seeAllText: {
    ...appStyles.body2,
    fontSize: 12,
    color: colors.primary,
  },
  tabContent: {
    flex: 1,
    paddingTop: 12,
  },
  headerRightContainer: {
    paddingHorizontal: 20,
    paddingVertical: 4,
    backgroundColor: colors.primary,
    borderRadius: 6,
  },
  shareButton: {
    fontFamily: fonts.NotoSans_Regular,
    color: colors.white,
    fontSize: 10,
  },
});
