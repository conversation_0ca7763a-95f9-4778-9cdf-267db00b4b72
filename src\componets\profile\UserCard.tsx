import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ViewStyle,
  TouchableOpacity,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import { images } from '../../assets/images';
import { colors, fonts } from '../../theme/theme';

export interface Props {
  name: string;
  weight: number;
  date: string;
  isMain?: boolean;
  imageUrl: string;
  containerStyle?: ViewStyle;
}

const UserCard: React.FC<Props> = ({
  name,
  weight,
  date,
  isMain,
  imageUrl,
  containerStyle,
}) => (
  <TouchableOpacity
    activeOpacity={0.5}
    style={[styles.container, containerStyle]}
  >
    <FastImage
      source={imageUrl ? { uri: imageUrl } : images.ImagePlaceholder}
      style={styles.image}
    />
    <View style={styles.info}>
      <Text style={styles.name}>
        {name} {isMain && <Text style={styles.main}>(main user)</Text>}
      </Text>
      <Text style={styles.date}>{date}</Text>
    </View>
    <Text style={styles.weight}>{weight.toFixed(2)} kg</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 10,
    backgroundColor: colors.white,
    borderRadius: 12,
    justifyContent: 'space-between',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  info: {
    flex: 1,
    marginLeft: 10,
    alignItems: 'flex-start',
  },
  name: {
    fontSize: 14,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_100,
  },
  main: {
    fontSize: 12,
    fontFamily: fonts.Catamaran_Medium,
    color: '#8D8D8D',
    lineHeight: 20,
  },
  date: {
    fontSize: 10,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_30,
  },
  weight: {
    fontSize: 12,
    fontFamily: fonts.NotoSans_Regular,
    color: '#7E7E7E',
    paddingBottom: 12,
  },
});

export default UserCard;
