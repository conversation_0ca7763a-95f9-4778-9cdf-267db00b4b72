import { <PERSON><PERSON><PERSON>iew, StatusBar, StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import { appStyles, colors, sizes } from '../../../theme/theme';
import UserCard from '../../../componets/profile/UserCard';
import {
  ActionButtons,
  AnimatedProgressBar,
  AppText,
  HorizontalLine,
  InviteCard,
  ProfileHeader,
  HealthProfileCard,
  RequireLoginModal,
} from '../../../componets';
import {
  HeartStroke,
  SettingStroke,
  ProfileStroke,
} from '../../../assets/svgs';
import { StackParamList } from '../../../navigations/StackNavigator';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { useAppSelector } from '../../../redux/config/Store';
type Props = NativeStackScreenProps<StackParamList, 'Profile'>;

const Profile: React.FC<Props> = ({ navigation }) => {
  const user = useAppSelector(state => state.auth.user);
  console.log('user----', user);

  const [showHealthProfile, setShowHealthProfile] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);

  const handleProtectedAction = (action: () => void) => {
    if (!user?.id) {
      setShowLoginModal(true);
      return;
    }
    action();
  };
  const users = [
    {
      name: 'Martha R',
      weight: 90.12,
      date: 'Dec 26 , 2024',
      isMain: true,
      imageUrl:
        'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8cHJvZmlsZXxlbnwwfHwwfHx8MA%3D%3D',
    },
    {
      name: 'Milann k.',
      weight: 23.12,
      date: 'Dec 26 , 2024',
      imageUrl:
        'https://plus.unsplash.com/premium_photo-1673866484792-c5a36a6c025e?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8cHJvZmlsZXxlbnwwfHwwfHx8MA%3D%3D',
    },
    {
      name: 'Enzo E',
      weight: 19.2,
      date: 'Dec 26 , 2024',
      imageUrl:
        'https://plus.unsplash.com/premium_photo-1689977807477-a579eda91fa2?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTd8fHByb2ZpbGV8ZW58MHx8MHx8fDA%3D',
    },
  ];

  const healthProfileCards = [
    { title: 'My condition' },
    { title: 'Meds & supplements' },
    { title: 'Lifestyle' },
  ];
  const userData = {
    fullName: 'Martha Rojas',
    lastName: 'Doe',
    dob: '1995-01-01',
    gender: 'female',
    height: '160',
    weight: '65',
    modeSelection: 'Manual',
    avatarUri:
      'https://plus.unsplash.com/premium_photo-1689568126014-06fea9d5d341?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MXx8cHJvZmlsZXxlbnwwfHwwfHx8MA%3D%3D',
  };
  return (
    <View style={styles.container}>
      <StatusBar
        backgroundColor={colors.background_color}
        barStyle={'dark-content'}
      />
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 16, paddingHorizontal: 4 }}
      >
        {/* PROFILE HEADER */}
        <ProfileHeader
          onPress={() =>
            handleProtectedAction(() =>
              navigation.navigate('EditProfile', { userData }),
            )
          }
        />
        {/* HORIZONTAL LINE */}
        <HorizontalLine marginTop={12} />
        {/* PROGRESS BAR */}
        <View style={appStyles.flexBtw}>
          <AnimatedProgressBar
            height={6}
            fillColor="#aacefe"
            backgroundColor="#e0edfa"
            max={100}
            value={78}
            containerStyle={{ width: '60%' }}
          />

          <AppText style={[appStyles.h4, { color: '#7F909F' }]}>
            78% completion
          </AppText>
        </View>
        {/*  ACTIION BUTTONS*/}
        <View style={[appStyles.flexBtw, { marginTop: 18 }]}>
          <ActionButtons
            title="Add user"
            icon={<ProfileStroke />}
            onPressCard={() =>
              handleProtectedAction(() =>
                navigation.navigate('EditProfile', {}),
              )
            }
          />
          <ActionButtons
            title="Health profile"
            icon={<HeartStroke />}
            containerStyle={{
              marginHorizontal: 12,
              backgroundColor: colors.grey_10,
              borderWidth: 2,
              borderColor: showHealthProfile ? colors.primary : colors.grey_10,
            }}
            onPressCard={() => setShowHealthProfile(!showHealthProfile)}
          />
          <ActionButtons
            title="Settings"
            icon={<SettingStroke />}
            onPressCard={() =>
              handleProtectedAction(() => navigation.navigate('Settings'))
            }
          />
        </View>

        {/* Conditional content based on showHealthProfile */}
        {showHealthProfile ? (
          <View style={styles.healthProfileList}>
            {healthProfileCards.map((card, i) => (
              <HealthProfileCard
                key={i}
                title={card.title}
                onPress={() =>
                  handleProtectedAction(() =>
                    navigation.navigate('HealthProfileDetail', {
                      title: card.title,
                    }),
                  )
                }
              />
            ))}
          </View>
        ) : (
          <View style={styles.userList}>
            {users.map((user, i) => (
              <UserCard
                key={i}
                name={user.name}
                weight={user.weight}
                date={user.date}
                isMain={user.isMain}
                imageUrl={user.imageUrl}
              />
            ))}
          </View>
        )}
        <InviteCard
          title="Share VitaeChek"
          paragraph="Invite your friend and share your experience to your friends"
          containerStyle={{ marginTop: 24, backgroundColor: '#D1E6FF' }}
        />
      </ScrollView>
      <RequireLoginModal
        visible={showLoginModal}
        onClose={() => setShowLoginModal(false)}
      />
    </View>
  );
};

export default Profile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal - 4,
    paddingTop: 18,
  },
  userList: {
    marginTop: 24,
    gap: 12,
  },
  healthProfileList: {
    marginTop: 24,
    gap: 12,
  },
});
