import { StyleSheet, Text, View, TextInput, ScrollView } from 'react-native';
import React, { useRef, useState } from 'react';
import {
  AppButton,
  CommentBox,
  DiabetesIndicator,
  MeasurementDate,
  MeasurementTypeSelector,
  TagSection,
} from '../../../componets';
import { colors, sizes } from '../../../theme/theme';
import { CustomRulerPicker } from '../../../componets/home/<USER>';
import { Tags } from '../../../constants/Tags';
import { toggleTagInSections } from '../../../helper/toggleTagInSections';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import RowCard from '../../../componets/home/<USER>';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { RectangleBlue } from '../../../assets/svgs';
type Props = NativeStackScreenProps<StackParamList, 'MeasureBloodGlucose'>;

type TagSectionType = {
  title: string;
  tags: string[];
  selectedTags: string[];
  addButtonText?: string;
};

const MeasureManualBloodGlucose: React.FC<Props> = ({ navigation }) => {
  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const [comment, setComment] = useState('');
  const [tagSections, setTagSections] = useState<TagSectionType[]>(Tags);
  const [selectedType, setSelectedType] = useState('pre-meal');
  const [showKetone, setShowKetone] = useState(false);

  const toggleTag = (sectionIndex: number, tag: string) => {
    setTagSections(prevSections =>
      toggleTagInSections(prevSections, sectionIndex, tag),
    );
  };

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        ref={scrollRef}
        enableOnAndroid
        keyboardShouldPersistTaps="handled"
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        extraScrollHeight={42}
      >
        <MeasurementDate
          label="Measurement Date"
          date="24/12/2024"
          time="10:24 AM"
          onPress={() => {
            // open date/time picker modal
          }}
        />

        <DiabetesIndicator
          label="Measurement Type"
          unit="2023 ACC/AHA"
          containerStyle={{ marginTop: 32 }}
        />
        {/* Glycemie ruler */}
        <CustomRulerPicker
          min={45}
          max={120}
          step={1}
          fractionDigits={0}
          onValueChange={number => {}}
          unit={'mg/dl'}
          title="Glycemie"
          indicatorColor={colors.primary}
          decelerationRate={'normal'}
          shortStepColor="#D1D5DB"
          longStepColor={'#E5E7EB'}
          longStepHeight={64}
          shortStepHeight={24}
          height={140}
          IndicatorLineSvg={RectangleBlue}
        />

        {showKetone ? (
          <CustomRulerPicker
            min={45}
            max={120}
            step={1}
            fractionDigits={0}
            onValueChange={number => {}}
            unit={'mg/dl'}
            title="Ketone"
            indicatorColor={colors.primary}
            decelerationRate={'normal'}
            shortStepColor="#D1D5DB"
            longStepColor={'#E5E7EB'}
            longStepHeight={64}
            shortStepHeight={24}
            height={100}
            IndicatorLineSvg={RectangleBlue}
          />
        ) : (
          <RowCard
            onPress={() => setShowKetone(true)}
            containerStyle={{ marginTop: 24 }}
            testID="row-card"
          />
        )}

        <MeasurementTypeSelector
          selectedKey={selectedType}
          onSelect={setSelectedType}
          containerStyle={{ marginTop: 20 }}
        />

        {tagSections.map((section, sectionIndex) => (
          <TagSection
            key={sectionIndex}
            title={section.title}
            tags={section.tags}
            selectedTags={section.selectedTags}
            onTagToggle={tag => toggleTag(sectionIndex, tag)}
            addButtonText={section?.addButtonText || ''}
            containerStyle={{ marginTop: 32 }}
          />
        ))}
        <CommentBox
          title="Comment"
          value={comment}
          onChangeText={setComment}
          placeholder="Write something nice..."
          maxLength={250}
          containerStyle={{ marginTop: 32 }}
          onFocus={() => scrollRef.current?.scrollToEnd()}
        />
        <AppButton
          title="Add Record"
          onPress={() => navigation.pop(2)}
          containerStyle={{ marginTop: 24 }}
        />
      </KeyboardAwareScrollView>
    </View>
  );
};

export default MeasureManualBloodGlucose;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 40,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 8,
    backgroundColor: colors.background_color,
  },
});
