import React from 'react';
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import { appStyles, colors, fonts } from '../../theme/theme';

interface CommentBoxProps extends TextInputProps {
  title: string;
  containerStyle?: ViewStyle;
}

const CommentBox: React.FC<CommentBoxProps> = ({
  title,
  containerStyle,
  ...textInputProps
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <Text style={styles.title}>{title}</Text>
      <TextInput
        style={styles.textInput}
        multiline
        textAlignVertical="top"
        {...textInputProps}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // marginVertical: 12,
  },
  title: {
    ...appStyles.h3,
  },
  textInput: {
    height: 120,
    borderWidth: 1,
    borderColor: '#D1D1D6',
    borderRadius: 14,
    padding: 12,
    fontSize: 14,
    backgroundColor: '#FCFCFC',
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_90,
    marginTop: 6,
  },
});

export default CommentBox;
