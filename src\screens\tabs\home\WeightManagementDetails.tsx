import { Image, StyleSheet, View } from 'react-native';
import React, { useState } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { appStyles, colors, fonts, sizes } from '../../../theme/theme';
import TopAnimatedTab from '../../../componets/common/TopAnimatedTab';
import { images } from '../../../assets/images';
import GoalProgressCard from '../../../componets/home/<USER>';
import { AppText } from '../../../componets';
type Props = NativeStackScreenProps<StackParamList, 'WeightManagementDetails'>;

const WeightManagementDetails: React.FC<Props> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<string>('Weight');
  const TABS = ['Weight', 'Body Fat'];

  return (
    <View style={styles.container}>
      {/* Top Tabs */}
      <View style={styles.topTabsContainer}>
        <TopAnimatedTab
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          TABS={TABS}
        />
      </View>
      <GoalProgressCard
        title="Lose Body Weight"
        startWeight={120}
        goalWeight={85}
        startDate="21 December 2024"
        endDate="15 March 2025"
        totalLoss={0}
        progressPercent={0}
      />
      <View style={styles.chartContainer}>
        <AppText style={styles.title}>Milestones</AppText>
        <Image
          resizeMode="contain"
          source={images.MilesStoneChart}
          style={{ width: '100%', height: 260 }}
        />
        <View style={appStyles.flexBtw}>
          <AppText style={styles.bodyText}>120.00 kg</AppText>
          <AppText style={styles.bodyText}>85.00 kg</AppText>
        </View>
        <AppText pt={16} style={[styles.title, { fontSize: 14 }]}>
          0/5 Milestones Completed
        </AppText>
        <AppText style={styles.bodyText2}>
          You have taken the fist step to success, keep it up!
        </AppText>
      </View>
    </View>
  );
};

export default WeightManagementDetails;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  topTabsContainer: {
    marginTop: 12,
  },
  tabContent: {
    flex: 1,
    paddingTop: 12,
  },
  chartContainer: {
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,
    paddingRight: 16,
    elevation: 1,
    paddingVertical: 12,
    borderRadius: 12,
    marginTop: 24,
    paddingLeft: 10,
  },
  title: {
    fontSize: 16,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
  },
  bodyText: {
    fontSize: 12,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
  },
  bodyText2: {
    fontSize: 12,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Medium,
    textAlign: 'center',
    paddingTop: 10,
  },
});
