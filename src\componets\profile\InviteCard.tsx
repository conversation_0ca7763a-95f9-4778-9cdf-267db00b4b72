import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { appStyles, colors, fonts } from '../../theme/theme';
import { ChevronRight } from '../../assets/svgs';

interface Props {
  title: string;
  paragraph: string;
  containerStyle?: ViewStyle;
}

const InviteCard: React.FC<Props> = ({ title, paragraph, containerStyle }) => (
  <View style={[styles.card, containerStyle]}>
    <Text style={styles.title}>{title}</Text>
    <Text style={styles.desc}>{paragraph}</Text>
    <TouchableOpacity
      style={[appStyles.flexRow, { paddingTop: 12 }]}
      activeOpacity={0.5}
    >
      <Text style={styles.link}>Invite now </Text>
      <ChevronRight stroke={colors.primary} />
    </TouchableOpacity>
  </View>
);

const styles = StyleSheet.create({
  card: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.grey_10,
    paddingVertical: 16,
    paddingHorizontal: 14,
  },
  title: {
    fontSize: 16,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_100,
  },
  desc: {
    fontSize: 14,
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Regular,
    paddingTop: 6,
  },
  link: {
    fontSize: 12,
    color: colors.primary,
    fontFamily: fonts.NotoSans_Medium,
  },
});

export default InviteCard;
