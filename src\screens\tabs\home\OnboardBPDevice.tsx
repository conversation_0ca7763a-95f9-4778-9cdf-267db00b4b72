import { Animated, FlatList, View } from 'react-native';
import React, { useRef, useState } from 'react';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { OnboardBPDeviceSlide } from '../../../constants/OnboardBPDevice';
import { AppButton } from '../../../componets';
import { onBoardStyles } from '../../../theme/theme';
import OnboardingSlide from '../../../componets/home/<USER>';
import PaginationDots from '../../../componets/home/<USER>';
type Props = NativeStackScreenProps<StackParamList, 'OnboardBPDevice'>;

const OnboardBPDevice: React.FC<Props> = ({ navigation }) => {
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useRef(new Animated.Value(0)).current;
  const [currentIndex, setCurrentIndex] = useState(0);
  const insets = useSafeAreaInsets();

  const handleNext = () => {
    if (currentIndex < OnboardBPDeviceSlide.length - 1) {
      flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
      setCurrentIndex(currentIndex + 1);
    } else {
      navigation.navigate('BPMonitor');
    }
  };

  const handleScroll = (event: any) => {
    const index = Math.round(
      event.nativeEvent.contentOffset.x /
        event.nativeEvent.layoutMeasurement.width,
    );
    setCurrentIndex(index);
  };
  console.log('currentIndex-------', currentIndex);

  return (
    <View style={[onBoardStyles.container, { paddingBottom: insets.bottom }]}>
      <View style={onBoardStyles.contentContainer}>
        <Animated.FlatList
          ref={flatListRef}
          data={OnboardBPDeviceSlide}
          horizontal
          pagingEnabled
          keyExtractor={item => item.id.toString()}
          renderItem={({ item }) => (
            <OnboardingSlide
              title={item.title}
              description={item.description}
              subDescription={item.subDescription}
              image={item.icon}
            />
          )}
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={handleScroll}
          onScroll={Animated.event(
            [{ nativeEvent: { contentOffset: { x: scrollX } } }],
            { useNativeDriver: false },
          )}
          scrollEventThrottle={16}
        />
        <PaginationDots
          count={OnboardBPDeviceSlide.length}
          currentIndex={currentIndex}
        />
      </View>

      <AppButton
        title={OnboardBPDeviceSlide[currentIndex].buttonText}
        onPress={handleNext}
        containerStyle={onBoardStyles.nextButton}
      />
    </View>
  );
};

export default OnboardBPDevice;
