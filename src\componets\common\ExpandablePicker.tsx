import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  FlatList,
  Platform,
  Keyboard,
} from 'react-native';
import { colors, fonts } from '../../theme/theme';

interface Props {
  data: string[];
  selectedValue: string;
  onValueChange: (val: string) => void;
  placeholder: string;
}

const ExpandablePicker: React.FC<Props> = ({
  data = [],
  selectedValue = '',
  onValueChange = (val: string) => {},
  placeholder,
}) => {
  const [expanded, setExpanded] = useState(false);
  const [animation] = useState(new Animated.Value(50)); // Initial height

  const togglePicker = () => {
    Keyboard.dismiss();
    const initialHeight = 58;
    const expandedHeight = initialHeight + data.length * 50 + 10; // 50 per item + padding

    Animated.timing(animation, {
      toValue: expanded ? initialHeight : expandedHeight,
      duration: 300,
      useNativeDriver: false,
    }).start();

    setExpanded(!expanded);
  };

  const handleSelect = (item: string) => {
    onValueChange(item);
    togglePicker();
  };

  return (
    <Animated.View style={[styles.container, { height: animation }]}>
      <TouchableOpacity onPress={togglePicker} style={styles.trigger}>
        <Text
          style={selectedValue ? styles.selectedText : styles.placeholderText}
        >
          {selectedValue || placeholder}
        </Text>
        <View
          style={[
            styles.triangle,
            {
              borderBottomWidth: expanded ? 6 : 0,
              borderTopWidth: expanded ? 0 : 6,
            },
          ]}
        />
      </TouchableOpacity>

      {expanded && (
        <FlatList
          data={data}
          keyExtractor={item => item}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => handleSelect(item)}
              style={styles.option}
            >
              <Text style={styles.optionText}>{item}</Text>
            </TouchableOpacity>
          )}
          showsVerticalScrollIndicator={false}
          style={styles.optionsContainer}
          contentContainerStyle={{ paddingBottom: 10 }}
        />
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 10,
    justifyContent: 'center',
    paddingHorizontal: 16,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
    marginHorizontal: 2,
  },
  trigger: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    height: 48,
    backgroundColor: colors.white,
  },
  selectedText: {
    fontSize: 16,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
  },
  placeholderText: {
    fontSize: 14,
    color: '#51535A',
    fontFamily: fonts.NotoSans_Regular,
  },

  optionsContainer: {
    marginTop: 10,
  },
  option: {
    paddingVertical: 12,
    borderTopWidth: 1,
    borderColor: '#F0F0F0',
  },
  optionText: {
    fontSize: 16,
    color: '#333',
  },
  triangle: {
    width: 0,
    height: 0,
    borderLeftWidth: 6,
    borderRightWidth: 6,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderBottomColor: '#3F3F3F',
    zIndex: 100,
  },
});

export default ExpandablePicker;
