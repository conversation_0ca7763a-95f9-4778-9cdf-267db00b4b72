import React from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { CalendarFill, ClockFill } from '../../assets/svgs';
import { appStyles, colors, fonts } from '../../theme/theme';
import AppText from '../common/AppText';
// import Icon from 'react-native-vector-icons/Feather';

export type MeasurementDateProps = {
  label: string;
  date: string;
  time: string;
  onPress: () => void;
  changeText?: string;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
  dateTextStyle?: TextStyle;
  changeTextStyle?: TextStyle;
};

const MeasurementDate: React.FC<MeasurementDateProps> = ({
  label,
  date,
  time,
  onPress,
  changeText = 'Click to change',
  containerStyle,
  labelStyle,
  dateTextStyle,
  changeTextStyle,
}) => {
  return (
    <TouchableOpacity
      activeOpacity={0.5}
      style={[styles.container, containerStyle]}
      onPress={onPress}
    >
      <View style={styles.row}>
        <AppText style={[styles.label, labelStyle]}>{label}</AppText>
        <AppText style={[styles.changeText, changeTextStyle]}>
          {changeText}
        </AppText>
      </View>

      <View style={styles.dateRow}>
        <View style={styles.dateTimeGroup}>
          <CalendarFill />
          <AppText style={[styles.dateText, dateTextStyle]}>{date}</AppText>
        </View>

        <View style={styles.dateTimeGroup}>
          <ClockFill />
          <AppText style={[styles.dateText, dateTextStyle]}>{time}</AppText>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default MeasurementDate;
const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingBottom: 6,
  },
  label: {
    ...appStyles.h3,
  },
  changeText: {
    fontSize: 12,
    color: colors.grey_50,
    fontFamily: fonts.Catamaran_Regular,
  },
  dateRow: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    gap: 18,
  },
  dateTimeGroup: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateText: {
    ...appStyles.h5,
    color: colors.grey_90,
  },
});
