import { StyleSheet, View, ViewStyle } from 'react-native';
import React, { useState, memo } from 'react';
import AppText from '../common/AppText';
import ToggleSwitch from 'toggle-switch-react-native';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';

// ✅ Moved outside to prevent unnecessary re-renders
const WeightItem = memo(
  ({
    label,
    isOn,
    onToggle,
    containerStyle,
  }: {
    label: string;
    isOn: boolean;
    onToggle: (val: boolean) => void;
    containerStyle?: ViewStyle;
  }) => {
    return (
      <View style={[appStyles.flexBtw, containerStyle]}>
        <View>
          <AppText style={styles.largeTitle}>08:00</AppText>
          <AppText style={styles.paragraph}>{label}</AppText>
        </View>
        <ToggleSwitch
          isOn={isOn}
          onColor={colors.primary}
          offColor="#A0AEC0"
          size="small"
          onToggle={onToggle}
        />
      </View>
    );
  },
);

const WeightReminder = () => {
  const [switchStates, setSwitchStates] = useState({
    first: false,
    second: false,
    third: false,
  });

  return (
    <View style={styles.weightContainer}>
      <WeightItem
        label="First"
        isOn={switchStates.first}
        onToggle={val => setSwitchStates(prev => ({ ...prev, first: val }))}
        containerStyle={{ marginTop: 16 }}
      />
      <WeightItem
        label="Second"
        isOn={switchStates.second}
        onToggle={val => setSwitchStates(prev => ({ ...prev, second: val }))}
        containerStyle={{ marginTop: 32 }}
      />
      <WeightItem
        label="Third"
        isOn={switchStates.third}
        onToggle={val => setSwitchStates(prev => ({ ...prev, third: val }))}
        containerStyle={{ marginTop: 32 }}
      />
    </View>
  );
};

export default WeightReminder;

const styles = StyleSheet.create({
  weightContainer: {
    paddingHorizontal: sizes.paddingHorizontal,
  },
  largeTitle: {
    fontSize: 32,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    lineHeight: 42,
  },
  paragraph: {
    fontSize: 16,
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Regular,
    marginTop: -12,
  },
});
