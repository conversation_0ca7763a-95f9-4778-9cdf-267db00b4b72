import Doctors from './doctors/Doctors';
import Home from './home/<USER>';
import About from './profile/About';
import AccountSettings from './profile/AccountSettings';
import ChangePassword from './profile/ChangePassword';
import EditProfile from './profile/EditProfile';
import FAQs from './profile/FAQs';
import HelpCenter from './profile/HelpCenter';
import Profile from './profile/Profile';
import Settings from './profile/Settings';
import TermsAndConditions from './profile/TermsAndConditions';
import PrivacyPolicy from './profile/PrivacyPolicy';
import Resources from './resources/Resources';
import BloodGlucose from './home/<USER>';
import MeasureHealthMetrices from './home/<USER>';
import MyDevices from './home/<USER>';
import ScannerOnboarding from './home/<USER>';
import ScanResult from './home/<USER>';
import BloodPressure from './home/<USER>';
import MeasureBloodGlucose from './home/<USER>';
import MeasureManualBloodGlucose from './home/<USER>';
import MeasureBloodPressure from './home/<USER>';
import MeasureManualBloodPressure from './home/<USER>';
import BloodGlucoseDetails from './home/<USER>';
import OnboardBPDevice from './home/<USER>';
import OnboardBPScanner from './home/<USER>';
import BPMonitor from './home/<USER>';
import BPScanResult from './home/<USER>';
import FindBPDevices from './home/<USER>';
import BPMachineDetails from './home/<USER>';
import BPDetails from './home/<USER>';
import WeightManagement from './home/<USER>';
import SetWeightGoal from './home/<USER>';
import MeasureWeightManual from './home/<USER>';
import WeightManagementDetails from './home/<USER>';
import WeightDevices from './home/<USER>';
import ConnectWeightDevices from './home/<USER>';

export {
  Doctors,
  Home,
  Profile,
  Resources,
  EditProfile,
  Settings,
  AccountSettings,
  About,
  ChangePassword,
  FAQs,
  HelpCenter,
  TermsAndConditions,
  PrivacyPolicy,
  BloodGlucose,
  MeasureHealthMetrices,
  MyDevices,
  ScannerOnboarding,
  ScanResult,
  BloodPressure,
  MeasureBloodGlucose,
  MeasureManualBloodGlucose,
  MeasureBloodPressure,
  MeasureManualBloodPressure,
  BloodGlucoseDetails,
  OnboardBPDevice,
  OnboardBPScanner,
  BPMonitor,
  BPScanResult,
  FindBPDevices,
  BPMachineDetails,
  BPDetails,
  WeightManagement,
  SetWeightGoal,
  MeasureWeightManual,
  WeightManagementDetails,
  WeightDevices,
  ConnectWeightDevices,
};
