import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
} from 'react-native';
import { colors, fonts, sizes } from '../../theme/theme';
import AppText from '../common/AppText';

interface Props {
  title: string;
  icon?: React.ReactNode;
  containerStyle?: ViewStyle;
  onPress?: () => void;
}

const HealthProfileCard: React.FC<Props> = ({
  title,
  icon,
  containerStyle,
  onPress,
}) => (
  <TouchableOpacity
    onPress={onPress}
    activeOpacity={0.7}
    style={[styles.container, containerStyle]}
  >
    <View style={styles.content}>
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <AppText style={styles.title}>{title}</AppText>
    </View>
  </TouchableOpacity>
);

export default HealthProfileCard;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background_color,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.grey_20,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Medium,
    color: colors.grey_90,
    flex: 1,
  },
});
