import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { appStyles, colors, fonts } from '../../theme/theme';
import { Meals } from '../../constants/Meals';

export type MeasurementTypeOption = {
  key: string;
  label: string;
  icon: React.ReactNode;
  selectedIcon: React.ReactNode;
};

export type MeasurementTypeSelectorProps = {
  selectedKey: string;
  onSelect: (key: string) => void;
  containerStyle?: ViewStyle;
  labelStyle?: TextStyle;
};

const MeasurementTypeSelector: React.FC<MeasurementTypeSelectorProps> = ({
  selectedKey,
  onSelect,
  containerStyle,
  labelStyle,
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <Text style={[styles.label, labelStyle]}>Measurement Type---</Text>

      <View style={styles.optionsRow}>
        {Meals.map(option => {
          const isSelected = selectedKey === option.key;

          return (
            <TouchableOpacity
              key={option.key}
              style={[styles.option]}
              onPress={() => onSelect(option.key)}
              activeOpacity={0.5}
            >
              {isSelected ? <option.selectedIcon /> : <option.icon />}
              <Text
                style={[
                  styles.optionLabel,
                  isSelected && styles.optionLabelSelected,
                ]}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

export default MeasurementTypeSelector;
const styles = StyleSheet.create({
  container: {
    padding: 8,
    borderColor: colors.grey_05,
    borderRadius: 12,
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  label: {
    ...appStyles.h3,
  },
  optionsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  option: {
    alignItems: 'center',
    width: 70,
    marginTop: 4,
  },
  optionSelected: {
    backgroundColor: '#005DA6', // Blue background
    borderColor: '#005DA6',
  },
  optionLabel: {
    fontSize: 12,
    textAlign: 'center',
    color: colors.grey_30,
    fontFamily: fonts.NotoSans_Regular,
    paddingTop: 2,
  },
  optionLabelSelected: {
    color: colors.grey_90,
    fontFamily: fonts.NotoSans_Medium,
  },
});
