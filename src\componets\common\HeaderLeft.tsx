import { TouchableOpacity, ViewStyle } from 'react-native';
import React from 'react';
import { BackArrow } from '../../assets/svgs';
interface Props {
  onPress: () => void;
  containerStyle?: ViewStyle;
}

const HeaderLeft: React.FC<Props> = ({ onPress, containerStyle }) => {
  return (
    <TouchableOpacity
      activeOpacity={0.5}
      onPress={onPress}
      style={{
        width: 30,
        alignItems: 'center',
        height: 30,
        justifyContent: 'center',
        paddingTop: 4,
        ...containerStyle,
      }}
    >
      <BackArrow />
    </TouchableOpacity>
  );
};

export default HeaderLeft;
