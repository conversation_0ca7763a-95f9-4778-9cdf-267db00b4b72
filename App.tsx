import { LogBox, StatusBar, View } from 'react-native';
import React from 'react';
import RootStackNavigator from './src/navigations/RootStackNavigator';
import { colors } from './src/theme/theme';
import StoreContext from './src/redux/config/StoreContext';

const App = () => {
  LogBox.ignoreLogs([
    'VirtualizedLists should never be nested inside plain ScrollViews',
  ]);
  return (
    <StoreContext>
      <View style={{ flex: 1, backgroundColor: colors.background_color }}>
        <StatusBar
          backgroundColor={colors.background_color}
          barStyle={'dark-content'}
        />
        <RootStackNavigator />
      </View>
    </StoreContext>
  );
};

export default App;

// import React, { useRef, useState } from 'react';
// import {
//   Animated,
//   FlatList,
//   View,
//   Text,
//   StyleSheet,
//   TouchableOpacity,
//   Dimensions,
//   Image,
// } from 'react-native';
// import ReAnimated, { FadeInRight, FadeOutRight } from 'react-native-reanimated';
// import { colors, fonts, sizes } from './src/theme/theme';
// import { AppButton, AppText } from './src/componets';
// import { useSafeAreaInsets } from 'react-native-safe-area-context';

// const { width } = Dimensions.get('window');
// const onboardingData = [
//   {
//     id: '1',
//     image:
//       'https://images.unsplash.com/photo-1585892478446-503a54cd3aea?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8d2VpZ2h0JTIwZ29hbHxlbnwwfDF8MHx8fDA%3D',
//     title: 'What is Your Goal?',
//   },
//   {
//     id: '2',
//     image:
//       'https://images.unsplash.com/photo-1470167290877-7d5d3446de4c?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8Ym9keSUyMHdlaWdodHxlbnwwfDF8MHx8fDA%3D',
//     title: 'What is Your Goal Weight?',
//   },
//   {
//     id: '3',
//     image:
//       'https://plus.unsplash.com/premium_photo-1725075086634-0037c3040c54?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8Y2xvY2t8ZW58MHwxfDB8fHww',
//     title: 'Turn on Weight Reminders to help you reach your goals.',
//   },
//   {
//     id: '4',
//     image:
//       'https://images.unsplash.com/photo-1549190179-646f048c6108?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8Y2xvY2t8ZW58MHwxfDB8fHww',
//     title: 'Turn on Weight Reminders to help you reach your goals.',
//   },
//   {
//     id: '5',
//     image:
//       'https://images.unsplash.com/photo-1506784365847-bbad939e9335?w=900&auto=format&fit=crop&q=60&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NHx8Y2FsZW5kYXJ8ZW58MHwwfDB8fHww',
//     title: 'How soon do you plan to reach your goal?',
//   },
// ];

// const OnboardingScreen = () => {
//   const scrollX = useRef(new Animated.Value(0)).current;
//   const flatListRef = useRef<FlatList>(null);
//   const [currentIndex, setCurrentIndex] = useState(0);
//   const [selectedYourGoal, setSelectedYourGoal] = useState<string | null>(
//     'Lose Body Weights',
//   );

//   const handleNext = () => {
//     if (currentIndex < onboardingData.length - 1) {
//       flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
//     } else {
//       console.log('Onboarding finished!');
//       // Navigate or finish onboarding
//     }
//   };

//   const onViewableItemsChanged = useRef(({ viewableItems }: any) => {
//     if (viewableItems.length > 0) {
//       setCurrentIndex(viewableItems[0].index);
//     }
//   }).current;

//   const viewabilityConfig = {
//     itemVisiblePercentThreshold: 50,
//   };

//   const Your_Goal = [
//     'Lose Body Weight',
//     'Gain Body Weight',
//     'Maintain Body Weight',
//     'Reduce Body Fat',
//     'Increase Muscle Mass',
//   ];

//   const renderYourGoal = () => {
//     return (
//       <View style={styles.yourGoalContainer}>
//         {Your_Goal.map((item, index) => (
//           <TouchableOpacity
//             activeOpacity={0.5}
//             onPress={() => setSelectedYourGoal(item)}
//             key={index}
//             style={[
//               styles.yourGoalTextContainer,
//               {
//                 borderColor:
//                   selectedYourGoal === item ? colors.primary : colors.grey_30,
//               },
//             ]}
//           >
//             <AppText
//               style={[
//                 styles.yourGoalText,
//                 {
//                   color:
//                     selectedYourGoal === item ? colors.primary : colors.grey_30,
//                 },
//               ]}
//             >
//               {item}
//             </AppText>
//           </TouchableOpacity>
//         ))}
//       </View>
//     );
//   };
//   const renderGoalWeight = () => {
//     return (
//       <View>
//         <AppText style={{ color: 'red' }}>renderGoalWeight</AppText>
//       </View>
//     );
//   };
//   const renderItem = ({ item, index }: { item: any; index: number }) => {
//     const inputRange = [
//       (index - 1) * width,
//       index * width,
//       (index + 1) * width,
//     ];

//     const animatedOpacity = scrollX.interpolate({
//       inputRange,
//       outputRange: [0, 1, 0],
//       extrapolate: 'clamp',
//     });

//     return (
//       <View style={[styles.page, { width }]}>
//         <View style={styles.imageContainer}>
//           <Image
//             source={{ uri: item.image }}
//             style={styles.image}
//             resizeMode="cover"
//           />
//         </View>

//         <View style={styles.card}>
//           <Text style={styles.title}>{item.title}</Text>

//           <Animated.View style={{ opacity: animatedOpacity }}>
//             {index === 0 && renderYourGoal()}
//             {index === 1 && renderGoalWeight()}
//             {/* add others similarly */}
//           </Animated.View>
//         </View>
//       </View>
//     );
//   };

//   const renderDots = () => {
//     return (
//       <View style={styles.dotsContainer}>
//         {onboardingData.map((_, i) => {
//           const inputRange = [(i - 1) * width, i * width, (i + 1) * width];

//           const dotWidth = scrollX.interpolate({
//             inputRange,
//             outputRange: [8, 24, 8],
//             extrapolate: 'clamp',
//           });

//           const dotColor = scrollX.interpolate({
//             inputRange,
//             outputRange: ['#D9DFE6', '#39434F', '#D9DFE6'],
//             extrapolate: 'clamp',
//           });

//           return (
//             <Animated.View
//               key={i}
//               style={[
//                 styles.dot,
//                 {
//                   width: dotWidth,
//                   backgroundColor: dotColor,
//                 },
//               ]}
//             />
//           );
//         })}
//       </View>
//     );
//   };
//   return (
//     <View style={styles.container}>
//       <Animated.FlatList
//         ref={flatListRef}
//         horizontal
//         pagingEnabled
//         showsHorizontalScrollIndicator={false}
//         data={onboardingData}
//         keyExtractor={item => item.id}
//         renderItem={renderItem}
//         onScroll={Animated.event(
//           [{ nativeEvent: { contentOffset: { x: scrollX } } }],
//           { useNativeDriver: false },
//         )}
//         scrollEventThrottle={16}
//         onViewableItemsChanged={onViewableItemsChanged}
//         viewabilityConfig={viewabilityConfig}
//       />

//       {renderDots()}
//       <View style={styles.button}>
//         <AppButton
//           title={
//             currentIndex === onboardingData.length - 1 ? 'Finish' : 'Next Step'
//           }
//           onPress={handleNext}
//           containerStyle={{ width: '100%' }}
//         />
//       </View>
//     </View>
//   );
// };

// export default OnboardingScreen;

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.white,
//   },
//   page: {
//     flex: 1,
//     alignItems: 'center',
//     justifyContent: 'flex-start',
//   },
//   imageContainer: {
//     width: '100%',
//     height: 400,
//     justifyContent: 'center',
//     alignItems: 'center',
//     backgroundColor: '#9c1c27',
//   },
//   image: {
//     width: '100%',
//     height: '100%',
//   },
//   card: {
//     position: 'absolute',
//     bottom: 0,
//     width: '100%',
//     borderTopLeftRadius: 20,
//     borderTopRightRadius: 20,
//     backgroundColor: colors.white,
//     height: sizes.height - 400,
//     zIndex: 20,
//     paddingTop: 54,
//   },
//   title: {
//     fontSize: 24,
//     fontFamily: fonts.Catamaran_SemiBold,
//     color: colors.grey_90,
//     textAlign: 'center',
//   },
//   dotsContainer: {
//     flexDirection: 'row',
//     justifyContent: 'center',
//     position: 'absolute',
//     top: 336,
//     left: 24,
//   },
//   dot: {
//     height: 8,
//     borderRadius: 4,
//     marginHorizontal: 6,
//   },
//   button: {
//     borderRadius: 10,
//     alignItems: 'center',
//     paddingTop: 12,
//     paddingBottom: 32,
//     backgroundColor: colors.white,
//     paddingHorizontal: sizes.paddingHorizontal - 4,
//     shadowColor: '#000',
//     shadowOffset: {
//       width: 0,
//       height: 3,
//     },
//     shadowOpacity: 0.29,
//     shadowRadius: 4.65,

//     elevation: 7,
//   },
//   yourGoalContainer: {
//     flexWrap: 'wrap',
//     paddingHorizontal: sizes.paddingHorizontal,
//     gap: 8,
//   },
//   yourGoalTextContainer: {
//     paddingVertical: 14,
//     borderRadius: 8,
//     backgroundColor: '#ECEFF2',
//     width: '100%',
//     borderWidth: 1,
//     borderColor: colors.primary,
//   },
//   yourGoalText: {
//     fontSize: 16,
//     fontFamily: fonts.NotoSans_Medium,
//     color: colors.grey_30,
//     textAlign: 'center',
//   },
// });

// import React, { useState } from 'react';
// import { View, StyleSheet } from 'react-native';
// import DurationPicker from './src/componets/home/<USER>';

// const numberData = [...Array(100).keys()].map(index => ({
//   value: index,
//   label: index.toString(),
// }));

// const unitData = [
//   { value: 1, label: 'Weeks' },
//   { value: 2, label: 'Days' },
// ];

// const App = () => {
//   const [selectedNumber, setSelectedNumber] = useState(0);
//   const [selectedUnit, setSelectedUnit] = useState(unitData[0].value);

//   return (
//     <View style={styles.container}>
//       <DurationPicker
//         numberData={numberData}
//         unitData={unitData}
//         selectedNumber={selectedNumber}
//         selectedUnit={selectedUnit}
//         setSelectedNumber={setSelectedNumber}
//         setSelectedUnit={setSelectedUnit}
//       />
//     </View>
//   );
// };

// export default App;

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//   },
// });
