<svg width="21" height="106" viewBox="0 0 21 106" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1036_57658)">
<rect x="4.5" y="4" width="12" height="98" rx="4" fill="url(#paint0_linear_1036_57658)"/>
</g>
<defs>
<filter id="filter0_d_1036_57658" x="0.5" y="0" width="20" height="106" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="4" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_1036_57658"/>
<feOffset/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.568627 0 0 0 0 0.568627 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1036_57658"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1036_57658" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1036_57658" x1="10.5" y1="4" x2="10.5" y2="102" gradientUnits="userSpaceOnUse">
<stop stop-color="#21A3F2"/>
<stop offset="1" stop-color="#0873B4"/>
</linearGradient>
</defs>
</svg>
