import React from 'react';
import { StyleSheet, View } from 'react-native';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { AppButton, AppText, FormInput, SocialButton } from '../../componets';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import { FacebookLogo, GoogleLogo } from '../../assets/svgs';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StackParamList } from '../../navigations/StackNavigator';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
type Props = NativeStackScreenProps<StackParamList, 'Login'>;

// Validation schema using Yup
const LoginSchema = Yup.object().shape({
  identifier: Yup.string().email('Invalid email').required('Email is required'),
  password: Yup.string().min(6, 'Too short').required('Password is required'),
});
const Login: React.FC<Props> = ({ navigation }) => {
  const insets = useSafeAreaInsets();
  // Initialize Formik
  const formik = useFormik({
    initialValues: {
      identifier: '',
      password: '',
    },
    validationSchema: LoginSchema,
    enableReinitialize: true,
    onSubmit: async values => {
      navigation.navigate('HealthAssessment');
    },
  });

  const { handleChange, handleBlur, handleSubmit, values, errors, touched } =
    formik;

  return (
    <KeyboardAwareScrollView
      contentContainerStyle={styles.scrollContent}
      keyboardShouldPersistTaps="handled"
      showsVerticalScrollIndicator={false}
    >
      <View style={[styles.container, { paddingBottom: insets.bottom + 20 }]}>
        {/* Header section */}
        <View>
          <AppText style={appStyles.largeTitle}>Login</AppText>
          <AppText pt={20} style={appStyles.h1}>
            Welcome John!
          </AppText>
          <AppText pt={12} style={appStyles.body3}>
            Login to continue
          </AppText>

          {/* Email input */}
          <FormInput
            label="Email"
            value={values.identifier}
            onChangeText={handleChange('identifier')}
            onBlur={handleBlur('identifier')}
            error={
              touched.identifier && errors.identifier ? errors.identifier : ''
            }
            keyboardType="email-address"
            autoCapitalize="none"
            containerStyle={styles.inputSpacing}
          />

          {/* Password input */}
          <FormInput
            label="Password"
            value={values.password}
            onChangeText={handleChange('password')}
            onBlur={handleBlur('password')}
            error={touched.password && errors.password ? errors.password : ''}
            isPassword
            autoCapitalize="none"
            containerStyle={styles.passwordSpacing}
          />
        </View>
        <View>
          {/* OR separator */}
          <View style={appStyles.orContainer}>
            <View style={appStyles.horizontalLine} />
            <AppText px={12} style={appStyles.body3}>
              Or
            </AppText>
            <View style={appStyles.horizontalLine} />
          </View>

          {/* Social buttons */}
          <SocialButton
            title="Continue with Google"
            icon={<GoogleLogo />}
            containerStyle={styles.socialButtonSpacing}
            onPress={() => {}}
          />
          <SocialButton
            title="Continue with Facebook"
            icon={<FacebookLogo />}
            containerStyle={styles.facebookButtonSpacing}
            onPress={() => {}}
          />

          {/* Footer section: Login button + Sign up link */}
          <AppButton
            title="Login"
            onPress={handleSubmit}
            containerStyle={styles.loginButton}
            disabled={!formik.isValid || !formik.dirty}
          />

          <AppText style={appStyles.footerText}>
            Don’t have an account?{' '}
            <AppText
              onPress={() => navigation.replace('PhoneAuth')}
              style={appStyles.signupText}
            >
              Sign up
            </AppText>
          </AppText>
        </View>
      </View>
    </KeyboardAwareScrollView>
  );
};

export default Login;

const styles = StyleSheet.create({
  scrollContent: {
    flexGrow: 1,
  },
  container: {
    flex: 1,
    justifyContent: 'space-between',
    backgroundColor: colors.background_color,
    paddingTop: 12,
    paddingHorizontal: sizes.paddingHorizontal,
  },
  inputSpacing: {
    marginTop: 12,
  },
  passwordSpacing: {
    marginTop: 16,
  },

  socialButtonSpacing: {
    marginTop: 24,
  },
  facebookButtonSpacing: {
    marginTop: 16,
  },
  loginButton: {
    marginTop: 36,
  },
});
