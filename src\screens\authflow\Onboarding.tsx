import React, { useRef, useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Platform,
  UIManager,
  LayoutAnimation,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import AppIntroSlider from 'react-native-app-intro-slider';
import { ON_BOARDING } from '../../constants/Onboarding';
import { colors, fonts, sizes } from '../../theme/theme';
import { AppText } from '../../componets';
import AppButton from '../../componets/common/AppButton';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../navigations/StackNavigator';
type Props = NativeStackScreenProps<StackParamList, 'Onboarding'>;

// Enable LayoutAnimation on Android

if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
const Onboarding: React.FC<Props> = ({ navigation }) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const sliderRef = useRef<AppIntroSlider | null>(null);

  // Dynamically return button title based on the current slide index
  const getButtonTitle = (index: number): string => {
    if (index === 0) return 'Get Started';
    if (index === 3) return 'Let’s go';
    return 'Continue';
  };

  // Render each slide's content
  const renderItem = ({ item }: { item: (typeof ON_BOARDING)[0] }) => (
    <View style={styles.slide}>
      <FastImage
        source={item.image}
        style={styles.imageContainer}
        resizeMode="cover"
      />
    </View>
  );

  // Custom pagination UI with dots, title, and buttons
  const renderPagination = (activeIndex: number) => {
    return (
      <View
        style={[
          styles.paginationContainer,
          { backgroundColor: ON_BOARDING[activeIndex].color },
        ]}
        key={activeIndex}
      >
        <View style={styles.paginationDots}>
          {ON_BOARDING.map((_, i) => (
            <TouchableOpacity
              key={i}
              style={[i === activeIndex ? styles.activeDot : styles.dot]}
              onPress={() => sliderRef.current?.goToSlide(i, true)}
            />
          ))}
        </View>
        <AppText style={styles.paginationTitle}>
          {ON_BOARDING[activeIndex].title}
        </AppText>
        {activeIndex === 2 ? (
          <View style={{ marginTop: 10 }}>
            <AppText style={styles.paginationParagraph}>
              It’s all about you:
            </AppText>
            <AppText style={styles.paginationParagraph}>
              1. Individual health goals
            </AppText>
            <AppText style={styles.paginationParagraph}>
              2. Customizes insights & tips
            </AppText>
            <AppText style={styles.paginationParagraph}>
              3. Your own health coach
            </AppText>
          </View>
        ) : (
          <AppText mt={10} style={styles.paginationParagraph}>
            {ON_BOARDING[activeIndex].paragraph}
          </AppText>
        )}
        <AppButton
          title={getButtonTitle(activeIndex)}
          containerStyle={styles.getStartedButton}
          onPress={() => {
            if (activeIndex === 3) {
              navigation.replace('PhoneAuth');
            } else {
              sliderRef.current?.goToSlide(activeIndex + 1, true);
            }
          }}
        />
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <AppIntroSlider
        keyExtractor={item => item.id.toString()}
        renderItem={renderItem}
        renderPagination={renderPagination}
        data={ON_BOARDING}
        ref={sliderRef}
        onSlideChange={index => {
          // Animate height/position changes on slide change
          // LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
          setActiveIndex(index);
        }}
      />
    </View>
  );
};

export default Onboarding;
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
  },
  slide: {
    flex: 1,
  },
  imageContainer: {
    width: '100%',
    height: sizes.height + 20,
    overflow: 'hidden',
  },
  paginationContainer: {
    backgroundColor: colors.cyan_blue,
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    padding: 24,
    borderTopRightRadius: 20,
    borderTopLeftRadius: 20,
    paddingBottom: 38,
  },
  paginationTitle: {
    fontSize: 34,
    color: colors.grey_90,
    fontFamily: fonts.Catamaran_SemiBold,
    lineHeight: 42,
    paddingRight: sizes.width > 400 ? 48 : 32,
    paddingTop: 24,
  },
  paginationParagraph: {
    fontSize: 20,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    lineHeight: 34,
  },
  getStartedButton: {
    marginTop: 24,
  },

  paginationDots: {
    height: 8,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
    alignSelf: 'flex-start',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 6,
    backgroundColor: colors.white,
  },
  activeDot: {
    width: 24,
    height: 8,
    borderRadius: 6,
    marginHorizontal: 4,
    backgroundColor: colors.grey_90,
  },
});
