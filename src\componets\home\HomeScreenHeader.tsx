import React from 'react';
import { StyleSheet, TextInput, View, TextInputProps } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors, fonts } from '../../theme/theme';
import ProfileHeader from '../profile/ProfileHeader';
import AppText from '../common/AppText';
import { HamBurgerMenu, MicroPhone } from '../../assets/svgs';

interface Props {
  name: string;
  onPressIcon: () => void;
}
const HomeScreenHeader: React.FC<Props> = ({ name, onPressIcon }) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <ProfileHeader
        name="Good Morning"
        subtitle={name}
        subTitleStyle={styles.subTitleStyle}
        nameStyle={styles.nameStyle}
        size={50}
        isChevron={false}
        containerStyle={styles.profileHeaderContainer}
        icon={<HamBurgerMenu width={20} height={20} />}
        onPressIcon={onPressIcon}
      />

      <AppText style={styles.title}>How are you feeling on today?</AppText>

      <View style={styles.inputWrapper}>
        <TextInput
          placeholder="Tell us about your Health"
          placeholderTextColor={colors.white}
          style={styles.input}
        />
        <MicroPhone />
      </View>
    </View>
  );
};

export default HomeScreenHeader;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingBottom: 12,
    borderBottomRightRadius: 16,
    borderBottomLeftRadius: 16,
  },
  profileHeaderContainer: {
    marginTop: 24,
    paddingBottom: 12,
  },
  subTitleStyle: {
    fontSize: 18,
    color: colors.white,
    fontFamily: fonts.Catamaran_SemiBold,
    lineHeight: 24,
    paddingTop: 2,
  },
  nameStyle: {
    fontSize: 12,
    color: colors.white,
    fontFamily: fonts.NotoSans_Regular,
  },
  title: {
    fontSize: 30,
    color: colors.white,
    fontFamily: fonts.Catamaran_Regular,
    paddingTop: 10,
    letterSpacing: 1,
    lineHeight: 32,
  },
  inputWrapper: {
    height: 54,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: colors.white,
    paddingHorizontal: 16,
    backgroundColor: '#FFFFFF33',
    marginTop: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  input: {
    height: 54,
    color: colors.white,
    flex: 1,
  },
});
