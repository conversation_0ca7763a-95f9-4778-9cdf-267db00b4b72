import React from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  ImageSourcePropType,
} from 'react-native';
import { colors, fonts, sizes } from '../../theme/theme';

interface OnboardingSlideProps {
  title: string;
  description: string;
  subDescription: string;
  image: ImageSourcePropType;
}

const OnboardingSlide: React.FC<OnboardingSlideProps> = ({
  title,
  description,
  subDescription,
  image,
}) => {
  return (
    <View style={styles.slide}>
      <Image resizeMode="contain" style={styles.imageStyle} source={image} />
      <View style={{ flex: 0.5 }}>
        <Text style={styles.title}>{title}</Text>
        <Text style={styles.description}>{description}</Text>
        <Text style={styles.description}>{subDescription}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  slide: {
    width: sizes.width - sizes.paddingHorizontal * 2,
    paddingHorizontal: 12,
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 10,
    flex: 1,
  },

  imageStyle: {
    width: 200,
    height: 200,
    flex: 0.5,
  },
  title: {
    fontSize: 18,
    fontFamily: fonts.Catamaran_SemiBold,
    color: colors.grey_90,
    textAlign: 'center',
    marginBottom: 10,
  },
  description: {
    fontSize: 16,
    fontFamily: fonts.NotoSans_Regular,
    color: colors.grey_40,
    textAlign: 'center',
    lineHeight: 26,
    paddingBottom: 5,
  },
});

export default OnboardingSlide;
