import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { StackParamList } from '../../../navigations/StackNavigator';
import { colors, fonts, sizes } from '../../../theme/theme';
import { AppText, DropDownPicker } from '../../../componets';
import HealthProfileCard from '../../../componets/profile/HealthProfileCard';
import ToggleableTag from '../../../componets/common/ToggleableTag';
import CustomCheckbox from '../../../componets/common/CustomCheckbox';
import {
  getHealthProfileData,
  HealthProfileData,
} from '../../../data/healthProfileData';
import TagSection from '../../../componets/common/TagSection';

type Props = NativeStackScreenProps<StackParamList, 'HealthProfileDetail'>;

const HealthProfileDetail: React.FC<Props> = ({ route }) => {
  const { title } = route.params;

  // State for the entire health profile data
  const [profileData, setProfileData] = useState<HealthProfileData | null>(
    null,
  );

  // Initialize data based on the title
  React.useEffect(() => {
    const data = getHealthProfileData(title);
    if (data) {
      setProfileData(data);
    }
  }, [title]);

  const toggleRadioOption = (id: string) => {
    if (!profileData) return;

    setProfileData(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        radioOptions: prev.radioOptions.map(option => ({
          ...option,
          selected: option.id === id,
        })),
      };
    });
  };

  const toggleTag = (sectionIndex: number, tag: string) => {
    if (!profileData) return;

    setProfileData(prev => {
      if (!prev) return prev;
      const updatedTagSections = [...prev.tagSections];
      const section = updatedTagSections[sectionIndex];

      if (section.selectedTags.includes(tag)) {
        section.selectedTags = section.selectedTags.filter(t => t !== tag);
      } else {
        section.selectedTags = [...section.selectedTags, tag];
      }

      return {
        ...prev,
        tagSections: updatedTagSections,
      };
    });
  };

  const handleDropdownChange = (dropdownIndex: number, value: string) => {
    if (!profileData) return;

    setProfileData(prev => {
      if (!prev) return prev;
      const updatedDropdowns = [...prev.dropdowns];
      updatedDropdowns[dropdownIndex] = {
        ...updatedDropdowns[dropdownIndex],
        selectedValue: value,
      };

      return {
        ...prev,
        dropdowns: updatedDropdowns,
      };
    });
  };

  if (!profileData) {
    return (
      <View style={styles.container}>
        <AppText>Loading...</AppText>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Header card */}
        <HealthProfileCard title={title} />

        {/* Radio options */}
        {profileData.radioOptions.length > 0 && (
          <View>
            {profileData.radioOptions.map(option => (
              <CustomCheckbox
                key={option.id}
                checked={option.selected}
                onToggle={() => toggleRadioOption(option.id)}
                label={option.label}
                isRadioButton={true}
                containerStyle={styles.radioRow}
                labelStyles={styles.radioLabel}
              />
            ))}
          </View>
        )}

        {/* Dropdowns */}
        {profileData.dropdowns.map((dropdown, index) => (
          <DropDownPicker
            key={index}
            label={dropdown.label}
            options={dropdown.options}
            selectedValue={dropdown.selectedValue}
            onSelect={value => handleDropdownChange(index, value)}
          />
        ))}

        {/* Tag sections */}
        {profileData.tagSections.map((section, sectionIndex) => (
          <TagSection
            key={sectionIndex}
            title={section.title}
            tags={section.tags}
            selectedTags={section.selectedTags}
            onTagToggle={tag => toggleTag(sectionIndex, tag)}
            addButtonText={section.addButtonText}
          />
        ))}
      </ScrollView>
    </View>
  );
};

export default HealthProfileDetail;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background_color,
    paddingHorizontal: sizes.paddingHorizontal,
    paddingTop: 18,
  },
  scrollContent: {
    paddingBottom: 20,
  },

  radioRow: {
    marginBottom: 14,
  },
  radioLabel: {
    color: colors.grey_90,
  },
});
