// components/InfoCard.tsx
import React from 'react';
import { View, Text, StyleSheet, ViewStyle } from 'react-native';
import { appStyles, colors, fonts, sizes } from '../../theme/theme';
import AppText from '../common/AppText';

interface InfoCardProps {
  title: string;
  description: string;
  actionText?: string;
  containerStyle?: ViewStyle;
}

const InfoCard: React.FC<InfoCardProps> = ({
  title,
  description,
  actionText,
  containerStyle,
}) => {
  return (
    <View style={[styles.card, containerStyle]}>
      {/* Title */}
      <AppText style={[appStyles.h3, { lineHeight: 22 }]}>{title}</AppText>

      {/* Description */}
      <Text style={styles.description}>{description}</Text>

      {/* Optional Action */}
      {actionText ? (
        <AppText style={styles.action}>
          Action:<AppText style={styles.actionLabel}> {actionText}</AppText>
        </AppText>
      ) : // <AppText style={styles.action}>
      //   <AppText style={{ fontFamily: fonts.NotoSans_Medium }}>
      //     Action:{' '}
      //   </AppText>
      //   {actionText}
      // </AppText>
      null}
    </View>
  );
};

export default InfoCard;

const styles = StyleSheet.create({
  card: {
    marginHorizontal: sizes.paddingHorizontal,
    marginTop: 34,
    backgroundColor: colors.white,
    paddingHorizontal: 8,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.18,
    shadowRadius: 1.0,

    elevation: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '700',
    marginBottom: 8,
    fontFamily: fonts.NotoSans_Medium,
  },
  description: {
    fontSize: 14,
    color: '#6b7280', // Tailwind gray-500
    fontFamily: fonts.NotoSans_Medium,
  },
  action: {
    marginTop: 12,
    fontSize: 14,
    color: colors.grey_60,
    lineHeight: 20,
    fontFamily: fonts.NotoSans_Bold,
  },
  actionLabel: {
    fontFamily: fonts.NotoSans_Medium,
  },
});
