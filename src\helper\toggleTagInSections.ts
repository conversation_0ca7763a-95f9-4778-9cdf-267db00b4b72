// utils/tagSectionHelpers.ts

export type TagSectionType = {
  title: string;
  tags: string[];
  selectedTags: string[];
  addButtonText?: string;
};

/**
 * Returns a new updated TagSections array after toggling a tag.
 */
export function toggleTagInSections(
  prevSections: TagSectionType[],
  sectionIndex: number,
  tag: string,
): TagSectionType[] {
  return prevSections.map((section, index) => {
    if (index !== sectionIndex) return section;

    const alreadySelected = section.selectedTags.includes(tag);
    const updatedSelectedTags = alreadySelected
      ? section.selectedTags.filter(t => t !== tag)
      : [...section.selectedTags, tag];

    return {
      ...section,
      selectedTags: updatedSelectedTags,
    };
  });
}
