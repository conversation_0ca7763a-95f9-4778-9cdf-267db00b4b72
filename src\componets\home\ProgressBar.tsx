import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Slider from '@react-native-community/slider';
import { sizes } from '../../theme/theme';
import { images } from '../../assets/images';

interface ProgressBarProps {
  progress: number; // 0-100
  label: string;
  startValue: string;
  endValue: string;
  color?: string;
  backgroundColor?: string;
  showBar?: boolean;
}

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  label,
  startValue,
  endValue,
  color = '#03A9F4',
  backgroundColor = color,
  showBar = true,
}) => {
  const [value, setValue] = useState(progress / 100);

  if (!showBar) return null;

  return (
    <View style={styles.container}>
      <Slider
        value={value}
        onValueChange={setValue}
        minimumValue={0}
        maximumValue={1}
        minimumTrackTintColor="#5d1313ff"
        maximumTrackTintColor="#be7676ff"
      />

      {/* Range Text */}
      <View style={styles.rangeContainer}>
        <Text style={styles.rangeText}>{startValue}</Text>
        <Text style={styles.rangeText}>{endValue}</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginVertical: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  thumb: {
    height: 40,
    width: 40,
    borderRadius: 20,
    backgroundColor: '#5d1313ff',
    justifyContent: 'center',
    alignItems: 'center',
  },
  thumbText: {
    color: '#fff',
    fontSize: 12,
  },
  rangeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  rangeText: {
    fontSize: 12,
    color: '#555',
  },
});

export default ProgressBar;
