import React, { useState } from 'react';
import { StyleSheet, Text, View, TouchableOpacity } from 'react-native';
import { AppText, CustomCheckbox, AppButton } from '../../../componets';
import { colors, fonts, sizes } from '../../../theme/theme';

const PrivacyPolicy = () => {
  const [agreed, setAgreed] = useState(false);

  const handleAgreeToggle = () => {
    setAgreed(!agreed);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <AppText style={styles.lastUpdated}>
          Last Updated: 12th June, 2024
        </AppText>
      </View>
      <AppText style={styles.mainContent}>
        At VitaeChek, your privacy is our top priority. We collect personal
        information such as name, email, and medical history, along with health
        data like vital signs and appointment details, and usage data to improve
        your experience. This data is used to provide personalized healthcare
        services, manage appointments, and enhance app functionality. We do not
        sell your data; it is only shared with healthcare providers for care
        coordination or third-party services essential for app operations, all
        under strict confidentiality agreements. Your data is encrypted and
        stored securely, with advanced security measures in place to prevent
        unauthorized access. You can access, update, or request deletion of your
        data anytime by contacting us at{' '}
        <Text style={styles.emailText}><EMAIL></Text>. We may
        update this privacy policy periodically, and continued use of the app
        signifies acceptance of any changes. For any privacy-related concerns,
        please reach out to us at{' '}
        <Text style={styles.emailText}><EMAIL></Text>.
      </AppText>
      <View style={styles.agreementSection}>
        <CustomCheckbox
          checked={agreed}
          onToggle={handleAgreeToggle}
          label=""
          fillCheck={{ width: 20, height: 20 }}
          strokeCheck={{ width: 20, height: 20 }}
          containerStyle={styles.checkboxContainer}
        />
        <TouchableOpacity
          onPress={handleAgreeToggle}
          style={styles.agreementTextContainer}
        >
          <AppText style={styles.agreementText}>
            I agree to the{' '}
            <Text style={styles.linkText}>privacy policy of this app</Text>
          </AppText>
        </TouchableOpacity>
      </View>
      {/* {agreed && (
        <AppButton
          title="Continue"
          onPress={() => {
            // Handle continue action - can be customized based on use case
            console.log('User agreed to privacy policy');
          }}
          containerStyle={styles.continueButton}
        />
      )} */}
    </View>
  );
};

export default PrivacyPolicy;

const styles = StyleSheet.create({
  container: {
    backgroundColor: colors.background_color,
    padding: sizes.paddingHorizontal,
    flex: 1,
  },
  header: {
    marginBottom: 16,
  },
  lastUpdated: {
    fontSize: 12,
    color: colors.grey_60,
    fontFamily: fonts.NotoSans_Regular,
    textAlign: 'left',
  },
  mainContent: {
    fontSize: 12,
    lineHeight: 20,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
    marginBottom: 24,
  },
  agreementSection: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 25,
  },
  checkboxContainer: {
    marginTop: 2,
  },
  agreementTextContainer: {
    flex: 1,
  },
  agreementText: {
    fontSize: 14,
    lineHeight: 22,
    color: colors.grey_80,
    fontFamily: fonts.NotoSans_Regular,
  },
  linkText: {
    color: colors.primary,

    fontFamily: fonts.NotoSans_Medium,
  },
  emailText: {
    textDecorationLine: 'underline',
    color: colors.primary,

    fontFamily: fonts.NotoSans_Medium,
  },
  continueButton: {
    marginTop: 20,
    marginBottom: 16,
  },
});
